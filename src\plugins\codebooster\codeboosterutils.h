#ifndef CODEBOOSTERUTILS_H
#define CODEBOOSTERUTILS_H

#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QMimeDatabase>
#include <QMimeType>

#include <QStringList>
#include <coreplugin/messagemanager.h>
#include <utils/theme/theme.h>
#include <utils/stringutils.h>
#include <utils/stylehelper.h>
#include <QStandardPaths>
#include <QDateTime>

#include "codeboosterconstants.h"

using namespace Utils;

namespace CodeBooster::Internal{

enum MessageType
{
    Normal,
    Sucess,
    Error
};

static QString addPrefix(const QString &str, MessageType type)
{
    Utils::Theme::Color     themeColor = Utils::Theme::Token_Text_Muted;
    if (Sucess == type)     themeColor = Utils::Theme::Token_Notification_Success;
    else if (Error == type) themeColor = Utils::Theme::Token_Notification_Danger;

    auto qColorToAnsiCode = [] (const QColor &color) {
        return QString::fromLatin1("\033[38;2;%1;%2;%3m")
            .arg(color.red()).arg(color.green()).arg(color.blue());
    };

    const QColor fgColor = creatorTheme()->color(themeColor);
    const QColor ttColor = creatorTheme()->color(Utils::Theme::TextColorNormal);
    static const QString prefixString = qColorToAnsiCode(fgColor) + Constants::OUTPUT_PREFIX
                                        + qColorToAnsiCode(ttColor);

    QString timeStamp = QString(" %1 ").arg(QDateTime::currentDateTime().toString("hh:mm:ss"));

    return prefixString + timeStamp + str;
}

static void outputMessages(const QStringList &messages, MessageType type = Normal)
{
    for (const QString &msg : messages)
    {
        //QString message = Constants::OUTPUT_PREFIX + msg;
        QString message = addPrefix(msg, type);

        if (type == Error)
        {
            Core::MessageManager::writeDisrupting(message);
        }
        else if (type == Sucess)
        {
            Core::MessageManager::writeFlashing(message);
        }
        else
        {
            Core::MessageManager::writeSilently(message);
        }
    }
}

static void outputMessage(const QString &message, MessageType type = Normal)
{
    outputMessages({message}, type);
}

// TODO: 后期使用日志方式替换该函数
static void outputLog(const QString &message, MessageType type = Normal)
{
    outputMessages({message}, type);
}

// 开发模式专用日志函数，只在开发模式下输出
static void devLog(const QString &message, MessageType type = Normal)
{
    // 前向声明，避免循环依赖
    extern bool isDevMode();
    
    if (isDevMode()) {
        outputMessages({message}, type);
    }
}

static QString dataFolderPath()
{
    // 获取AppData/Local目录的路径
    static QString path = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation) +
        "/CodeBooster";

    // 确保文件夹路径存在，如果不存在则创建
    QDir dir(path);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    return path;
}

/**
 * @brief saveToTxtFile 将文本保存在文件中
 * @param text
 * @note 调试用的功能
 */
static void saveToTxtFile(const QString &text)
{
    // 获取当前时间并格式化为字符串
    QString currentDateTime = QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss");

    // 指定保存的文件路径，使用当前时间作为文件名
    QString filePath = QApplication::applicationDirPath() + "/output_" + currentDateTime + ".txt";

    // 创建一个QFile对象
    QFile file(filePath);

    // 打开文件，如果文件不存在则会创建新文件
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        // 创建一个QTextStream对象，用于写入文件
        QTextStream out(&file);

        // 设置文件编码
        out.setEncoding(QStringConverter::Latin1);

        // 将result文本写入文件
        out << text;

        // 关闭文件
        file.close();

        // 输出成功信息
        qDebug() << "文件保存成功:" << filePath;
        outputMessage(filePath);
    } else {
        // 输出错误信息
        qDebug() << "无法打开文件进行写入:" << filePath;
        outputMessage("无法打开文件进行写入:" , Error);
    }
}

static QString languageFromFileSuffix(const QString &fileSuffix)
{
    static const QMap<QString, QString> suffixToLanguage = {
        {"cpp", "cpp"},
        {"c", "c"},
        {"hpp", "cpp"},
        {"h", "cpp"},
        {"pro", "pro"}
    };

    return suffixToLanguage.value(fileSuffix, QString());
}


static bool fileIsTextFile(const QString &path) {
    // 创建 QMimeDatabase 对象
    QMimeDatabase mimeDatabase;

    // 获取文件的 MIME 类型
    QMimeType mimeType = mimeDatabase.mimeTypeForFile(path);

    // 检查文件是否是文本文件
    return mimeType.inherits("text/plain");
}


static QString readTextFile(const QString &filePath, bool success)
{
    success = true;

    // 创建 QFile 对象
    QFile file(filePath);

    // 检查文件是否存在并且是否可以打开
    if (!file.exists()) {
        qDebug() << "File does not exist:" << filePath;
        success = false;
        return QString();
    }

    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        success = false;
        qDebug() << "Could not open file:" << filePath;
        return QString();
    }

    // 创建 QTextStream 对象来读取文件内容
    QTextStream in(&file);
    QString fileContent = in.readAll();

    // 关闭文件
    file.close();

    QString text;
    text += QString("```%1\n").arg(file.fileName());
    text += fileContent;
    text += QString("\n```");

    return text;
}

}

#endif // CODEBOOSTERUTILS_H
