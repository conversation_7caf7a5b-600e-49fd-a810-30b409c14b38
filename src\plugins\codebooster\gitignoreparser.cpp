#include "gitignoreparser.h"
#include "codeboosterutils.h"
#include "codeboostersettings.h"
#include <QDebug>

namespace CodeBooster::Internal {

GitIgnoreParser::GitIgnoreParser()
{
    // 从设置中获取忽略规则
    loadRulesFromSettings();
}

void GitIgnoreParser::setIgnoreRules(const QString &ignoreText)
{
    clearRules();
    
    devLog(QString("开始解析 GitIgnore 规则，内容长度: %1").arg(ignoreText.length()));
    
    QStringList lines = ignoreText.split('\n', Qt::SkipEmptyParts);
    int validRuleCount = 0;
    int skippedLineCount = 0;
    
    for (const QString &line : lines) {
        QString trimmedLine = line.trimmed();
        
        // 跳过空行和注释行
        if (trimmedLine.isEmpty() || trimmedLine.startsWith('#')) {
            skippedLineCount++;
            devLog(QString("跳过空行或注释: %1").arg(line));
            continue;
        }
        
        IgnoreRule rule = parseRule(trimmedLine);
        if (!rule.pattern.isEmpty()) {
            mIgnoreRules.append(rule);
            validRuleCount++;
            devLog(QString("添加忽略规则: %1").arg(trimmedLine));
        } else {
        devLog(QString("无效规则行: %1").arg(trimmedLine));
        }
    }
    
    devLog(QString("GitIgnore 规则解析完成，共解析 %1 行，有效规则 %2 条，跳过 %3 行")
              .arg(lines.count()).arg(validRuleCount).arg(skippedLineCount));
}

void GitIgnoreParser::addIgnoreRule(const QString &rule)
{
    QString trimmedRule = rule.trimmed();
    if (!trimmedRule.isEmpty() && !trimmedRule.startsWith('#')) {
        IgnoreRule ignoreRule = parseRule(trimmedRule);
        if (!ignoreRule.pattern.isEmpty()) {
            mIgnoreRules.append(ignoreRule);
        }
    }
}

void GitIgnoreParser::clearRules()
{
    mIgnoreRules.clear();
}

bool GitIgnoreParser::shouldIgnoreFile(const QString &filePath, const QString &basePath) const
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        return false;
    }
    
    QString relativePath = getRelativePath(filePath, basePath);
    QString fileName = fileInfo.fileName();
    
    bool shouldIgnore = false;
    
    // 检查所有规则
    for (const IgnoreRule &rule : mIgnoreRules) {
        if (matchesRule(relativePath, rule, false) || matchesRule(fileName, rule, false)) {
            if (rule.isNegation) {
                shouldIgnore = false; // 否定规则，不忽略
            } else {
                shouldIgnore = true;  // 匹配忽略规则
            }
        }
    }
    
    return shouldIgnore;
}

bool GitIgnoreParser::shouldIgnoreDirectory(const QString &dirPath, const QString &basePath) const
{
    QFileInfo dirInfo(dirPath);
    if (!dirInfo.exists() || !dirInfo.isDir()) {
        return false;
    }
    
    QString relativePath = getRelativePath(dirPath, basePath);
    QString dirName = dirInfo.fileName();
    
    bool shouldIgnore = false;
    
    // 检查所有规则
    for (const IgnoreRule &rule : mIgnoreRules) {
        if (matchesRule(relativePath, rule, true) || matchesRule(dirName, rule, true)) {
            if (rule.isNegation) {
                shouldIgnore = false; // 否定规则，不忽略
            } else {
                shouldIgnore = true;  // 匹配忽略规则
            }
        }
    }
    
    return shouldIgnore;
}

void GitIgnoreParser::loadRulesFromSettings()
{
    try {
        QString rules = CodeBoosterSettings::instance().getGitIgnoreRules();
        setIgnoreRules(rules);
    } catch (const std::exception &e) {
        // 如果获取设置失败，使用默认规则
        devLog(QString("从设置加载 GitIgnore 规则失败，使用默认规则: %1").arg(e.what()));
        setIgnoreRules(getDefaultIgnoreRules());
    } catch (...) {
        // 处理其他异常
        devLog("从设置加载 GitIgnore 规则失败，使用默认规则");
        setIgnoreRules(getDefaultIgnoreRules());
    }
}

QString GitIgnoreParser::getDefaultIgnoreRules()
{
    return QString(
        "# 构建目录\n"
        "build/\n"
        "Build/\n"
        "BUILD/\n"
        "\n"
        "# Qt 用户文件\n"
        "*.user\n"
        "*.user.*\n"
        "\n"
        "# 隐藏文件和目录（以点开头）\n"
        ".*\n"
        "\n"
        "# 常见的临时文件和缓存\n"
        "*.tmp\n"
        "*.temp\n"
        "*.cache\n"
        "*.log\n"
        "\n"
        "# 编译产物\n"
        "*.o\n"
        "*.obj\n"
        "*.exe\n"
        "*.dll\n"
        "*.so\n"
        "*.dylib\n"
        "\n"
        "# IDE 相关\n"
        ".vscode/\n"
        ".idea/\n"
        "*.pro.user\n"
    );
}

GitIgnoreParser::IgnoreRule GitIgnoreParser::parseRule(const QString &rule) const
{
    IgnoreRule ignoreRule;
    QString pattern = rule;
    
    // 检查是否是否定规则（以 ! 开头）
    if (pattern.startsWith('!')) {
        ignoreRule.isNegation = true;
        pattern = pattern.mid(1); // 移除 ! 前缀
    }
    
    // 检查是否是目录规则（以 / 结尾）
    if (pattern.endsWith('/')) {
        ignoreRule.isDirectory = true;
        pattern = pattern.left(pattern.length() - 1); // 移除 / 后缀
    }
    
    ignoreRule.pattern = pattern;
    
    // 将模式转换为正则表达式
    QString regexPattern = patternToRegex(pattern);
    ignoreRule.regex = QRegularExpression(regexPattern, QRegularExpression::CaseInsensitiveOption);
    
    if (!ignoreRule.regex.isValid()) {
        qDebug() << "Invalid regex pattern:" << regexPattern << "from rule:" << rule;
        ignoreRule.pattern.clear(); // 标记为无效规则
    }
    
    return ignoreRule;
}

QString GitIgnoreParser::patternToRegex(const QString &pattern) const
{
    QString regex = QRegularExpression::escape(pattern);
    
    // 处理通配符
    regex.replace("\\*", ".*");     // * 匹配任意字符
    regex.replace("\\?", ".");      // ? 匹配单个字符
    
    // 如果模式不包含路径分隔符，则匹配任何位置的文件名
    if (!pattern.contains('/')) {
        regex = QString("(^|.*/)%1$").arg(regex);
    } else {
        regex = QString("^%1$").arg(regex);
    }
    
    return regex;
}

QString GitIgnoreParser::getRelativePath(const QString &fullPath, const QString &basePath) const
{
    if (basePath.isEmpty()) {
        return QFileInfo(fullPath).fileName();
    }
    
    QDir baseDir(basePath);
    return baseDir.relativeFilePath(fullPath);
}

bool GitIgnoreParser::matchesRule(const QString &path, const IgnoreRule &rule, bool isDirectory) const
{
    // 如果规则专门针对目录，但当前路径不是目录，则不匹配
    if (rule.isDirectory && !isDirectory) {
        return false;
    }
    
    return rule.regex.match(path).hasMatch();
}

} // namespace CodeBooster::Internal
