#include "contextitemwidget.h"
#include "ui_contextitemwidget.h"

#include <QMouseEvent>

#include <coreplugin/editormanager/editormanager.h>

#include "utils/utilsicons.h"
#include "codeboostericons.h"
#include "codeboostersettings.h"

namespace CodeBooster::Internal {

ContextItemWidget::ContextItemWidget(const ContextItem &context, QWidget *parent)
    : Q<PERSON>rame(parent)
    , ui(new Ui::ContextItemWidget)
    , mContext(context)
{
    ui->setupUi(this);
    this->setObjectName("ContextItemWidget");
    this->setCursor(Qt::PointingHandCursor);
    this->setFixedHeight(20);

    if (CB_SETTING.isDarkTheme())
    {
        mStyleSheet      = "QFrame#ContextItemWidget {background: #545556; border: 1px solid #5e5f60;} ";
        mHoverStyleSheet = "QFrame#ContextItemWidget {background: #545556; border: 1px solid #909090;}";
    }
    else
    {
        mStyleSheet      = "QFrame#ContextItemWidget {background: #e9e9e9; border: 1px solid #e2e2e2;}";
        mHoverStyleSheet = "QFrame#ContextItemWidget {background: #e9e9e9; border: 1px solid #cdcdcd;}";
    }

    // 根据上下文信息初始化
    {
        ui->label_icon->setFixedWidth(16);
        ui->label_icon->setAlignment(Qt::AlignCenter);
        ui->label_icon->setPixmap(mContext.icon().pixmap(QSize(16, 16)));

        ui->label_name->setFixedHeight(18);
        ui->label_name->setText(mContext.name);

        ui->label_type->setFixedHeight(18);
        ui->label_type->setAlignment(Qt::AlignCenter);
        ui->label_type->setText(mContext.typeDetail());

        this->setToolTip(context.description);
    }

    // 初始化按钮
    {
        mDiscardButton = new ButtonLabel(this);
        mDiscardButton->setIcon(Utils::Icons::CLOSE_TOOLBAR.icon());
        connect(mDiscardButton, &ButtonLabel::clicked, this, [this](QString text){
            Q_UNUSED(text)
            emit discard();
            mClickTriggerByDiscard = true;
        });
        ui->horizontalLayout->addWidget(mDiscardButton, 0);
    }

    updateStyleSheet(false);
}

ContextItemWidget::~ContextItemWidget()
{
    delete ui;
}

ContextItem ContextItemWidget::context() const
{
    return mContext;
}

void ContextItemWidget::setShowDiscardBtn(bool show)
{
    mDiscardButton->setVisible(show);
}

void ContextItemWidget::enterEvent(QEnterEvent *event)
{
    updateStyleSheet(true);
    QFrame::enterEvent(event);
}

void ContextItemWidget::leaveEvent(QEvent *event)
{
    updateStyleSheet(false);

    QFrame::leaveEvent(event);
}

void ContextItemWidget::mouseReleaseEvent(QMouseEvent *event)
{
    // 防止点击抛弃按钮时触发打开文件
    if (mClickTriggerByDiscard)
    {
        mClickTriggerByDiscard = false;
        return;
    }

    // 如果上下文是文件时打开
    if (ContextItem::File == mContext.type)
    {
        Core::EditorManager::openEditor(Utils::FilePath::fromString(mContext.uri));
    }
    QFrame::mouseReleaseEvent(event);
}

void ContextItemWidget::updateStyleSheet(bool hover)
{
    if (hover) this->setStyleSheet(mHoverStyleSheet);
    else       this->setStyleSheet(mStyleSheet);
}


// -------------------------------------------------------------------------
// ContextItemContainer
// -------------------------------------------------------------------------
ContextItemContainer::ContextItemContainer(bool showDiscardButton, QWidget *parent) :
    QFrame(parent),
    mShowDicardBtn(showDiscardButton)
{
    {
        mToolBar = new QToolBar(this);
        mExpandAction = new QAction(this);
        connect(mExpandAction, &QAction::triggered, this, &ContextItemContainer::onActionExpandTriggered);

        mInfoLabel = new QLabel(this);

        mToolBar->addAction(mExpandAction);
        mToolBar->addWidget(mInfoLabel);
    }

    {
        mContextArea = new QWidget(this);
        mContextArea->setVisible(false);
        QVBoxLayout *contextAreaLy = new QVBoxLayout(mContextArea);
        contextAreaLy->setSpacing(0);
        contextAreaLy->setContentsMargins(0, 0, 0, 0);
    }

    QVBoxLayout *ly = new QVBoxLayout(this);
    ly->setContentsMargins(0, 0, 0, 0);
    ly->setSpacing(0);
    ly->addWidget(mToolBar, 0);
    ly->addWidget(mContextArea, 1);

    updateActionState();
}

ContextItemContainer::~ContextItemContainer()
{

}

void ContextItemContainer::newContextItemWgt(const ContextItem &context)
{
    // 第一次添加上下文时显示列表
    if (allContext().isEmpty() && !mContextArea->isVisible())
    {
        showContext();
    }

    ContextItemWidget *contexWgt = new ContextItemWidget(context, mContextArea);
    contexWgt->setShowDiscardBtn(mShowDicardBtn);
    connect(contexWgt, &ContextItemWidget::discard, this, &ContextItemContainer::onDiscardContext);
    mContextArea->layout()->addWidget(contexWgt);

    updateInfoLabel();

    if (!isVisible())
    {
        show();
    }
}

QList<ContextItem> ContextItemContainer::allContext() const
{
    QList<ContextItem> items;
    for (auto wgt : contextWidgets())
    {
        items << wgt->context();
    }

    return items;
}

void ContextItemContainer::clearContext()
{
    for (auto wgt : contextWidgets())
    {
        mContextArea->layout()->removeWidget(wgt);
        wgt->deleteLater();
    }

    setVisible(false);
}

void ContextItemContainer::onActionExpandTriggered()
{
    mContextArea->setVisible(!mContextArea->isVisible());
    updateActionState();
}

void ContextItemContainer::updateActionState()
{
    if (mContextArea->isVisible())
    {
        mExpandAction->setIcon(EXPAND_ICON.icon());
        mExpandAction->setText("折叠");
    }
    else
    {
        mExpandAction->setIcon(COLLAPSE_ICON.icon());
        mExpandAction->setText("展开");
    }
}

void ContextItemContainer::showContext()
{
    mContextArea->setVisible(true);
    mExpandAction->setIcon(EXPAND_ICON.icon());
    mExpandAction->setText("折叠");
}

void ContextItemContainer::onDiscardContext()
{
    if (ContextItemWidget * contextWgt = qobject_cast<ContextItemWidget *>(sender()))
    {
        QList<ContextItemWidget *> wgts = contextWidgets();
        wgts.removeAll(contextWgt);

        emit contextRemoved(contextWgt->context());

        contextWgt->deleteLater();

        if (wgts.isEmpty())
        {
            this->hide();
        }
    }

    updateInfoLabel();
}

void ContextItemContainer::updateInfoLabel()
{
    int count = contextWidgets().size();
    mInfoLabel->setText(QString("%1 条上下文").arg(count));
}

QList<ContextItemWidget *> ContextItemContainer::contextWidgets() const
{
    return mContextArea->findChildren<ContextItemWidget *>();
}

//----------------------------------------------------------------
// ButtonLabel
//----------------------------------------------------------------
ButtonLabel::ButtonLabel(QWidget* parent)
    : QLabel(parent)
{
    // 设置鼠标指针为手指样式
    setCursor(Qt::PointingHandCursor);

    if (CB_SETTING.isDarkTheme())
    {
        mStyleSheet      = "background: #2e2f30; border: none; ";
        mHoverStyleSheet = "background: #545556; border: none;";
    }
    else
    {
        mStyleSheet      = "background: #e9e9e9; border: none; ";
        mHoverStyleSheet = "background: #ffffff; border: none; ";
    }

    setStyleSheet(mStyleSheet);
}

ButtonLabel::~ButtonLabel()
{
}

void ButtonLabel::setIcon(const QIcon &icon, QSize size)
{
    this->setPixmap(icon.pixmap(size));
}

void ButtonLabel::setText(const QString &text)
{
    QLabel::setText(text);
}

void ButtonLabel::enterEvent(QEnterEvent *event)
{
    setHoverStyle(true);
    QLabel::enterEvent(event);
}

void ButtonLabel::leaveEvent(QEvent* event)
{
    setHoverStyle(false);
    QLabel::leaveEvent(event);
}

void ButtonLabel::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit clicked(text());  // 发送点击信号
    }
    QLabel::mouseReleaseEvent(event);
}

void ButtonLabel::setHoverStyle(bool hover)
{
    if (hover) {
        setStyleSheet(mHoverStyleSheet);
    } else {
        setStyleSheet(mStyleSheet);
    }
}

}
