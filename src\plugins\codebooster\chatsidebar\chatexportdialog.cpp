#include "chatexportdialog.h"
#include "ui_chatexportdialog.h"

#include <QDebug>
#include <QPainter>
#include <QClipboard>
#include <QFileDialog>
#include <QDir>
#include <QRegularExpression>
#include <QtConcurrent>

#include "markdownpreview/markdownhtmlconverter.h"
#include "codeboosterutils.h"
#include "codeboostericons.h"
#include "instrumentor.h"
#include "codeboostersettings.h"

namespace CodeBooster::Internal{

ChatExportDialog::ChatExportDialog(const ChatSession &session, QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::ChatExportDialog)
    , mSession(session)
{
    ui->setupUi(this);

    // 设置窗口标志以包含最大化按钮
    setWindowFlags(windowFlags() | Qt::WindowMaximizeButtonHint);

    setWindowTitle("导出对话");
    resize(1024, 768);

    // 初始化选项输入控件
    {
        ui->label->setText("<b>导出格式</b><br>可以导出 Markdown 文本或者 PNG 图片");

        ui->comboBox_model->setMinimumHeight(24);
        ui->comboBox_model->setMinimumWidth(ui->pushButton_deSelectAll->width());
        ui->comboBox_model->addItems({"文本", "图片"});
        connect(ui->comboBox_model, static_cast<void (QComboBox::*)(const QString &)>(&QComboBox::currentTextChanged), this, [=](const QString text){
            if (text == "文本")
                ui->stackedWidget->setCurrentWidget(ui->page_text);
            else if (text == "图片")
                ui->stackedWidget->setCurrentWidget(ui->page_image);

            refreshPreview();
        });

        ui->lineEdit_search->setPlaceholderText("搜索消息");
        connect(ui->lineEdit_search, &QLineEdit::textEdited, this, &ChatExportDialog::onSearchLineEditTextChanged);

        connect(ui->pushButton_selectAll, &QPushButton::clicked, this, &ChatExportDialog::onSelectAllBtnClicked);
        connect(ui->pushButton_deSelectAll, &QPushButton::clicked, this, &ChatExportDialog::onDeselectAllBtnClicked);
    }

    // 初始化消息列表控件
    {
        ui->treeWidget_chat->setHeaderHidden(true);
        connect(ui->treeWidget_chat, &QTreeWidget::itemClicked, [](QTreeWidgetItem *item, int column) {
            // 切换 item 的选中状态
            if (item->isSelected())
            {
                if (item->checkState(column) == Qt::Checked)
                    item->setCheckState(column, Qt::Unchecked);
                else
                    item->setCheckState(column, Qt::Checked);
            }
        });
    }

    // 初始化预览控件属性
    {
        connect(ui->pushButton_copy, &QPushButton::clicked, this, &ChatExportDialog::onCopyBtnClicked);
        connect(ui->pushButton_export, &QPushButton::clicked, this, &ChatExportDialog::onExportBtnClicked);

        ui->stackedWidget->setCurrentWidget(ui->page_text);
        ui->textBrowser->setStyleSheet("QTextBrowser { background-color: #1f1f1f; color: #C5C5C5; }");

        ui->scrollArea->setBackgroundRole(QPalette::Dark);

        ui->label_image->setBackgroundRole(QPalette::Base);

        ui->spinBox_imageWidth->setRange(0.5, 4.0);
        ui->spinBox_imageWidth->setDecimals(1);
        ui->spinBox_imageWidth->setValue(1.0);
        ui->spinBox_imageWidth->setSuffix(" x");

        connect(ui->pushButton_refreshImage, &QPushButton::clicked, this, &ChatExportDialog::refreshPreview);
    }

    // 初始化加载预览图片相关
    {
        mSpinner = new SpinnerSolution::Spinner(SpinnerSolution::SpinnerSize::Large, ui->page_image);
        mSpinner->setVisible(false);

        connect(&mImageGenerationWatcher, &QFutureWatcher<QImage>::finished, this, [this]() {
            mImage = mImageGenerationWatcher.result();
            handleImageGenerated(mImage);
        });
    }

    // 加载对话
    loadChatSession();
    // 点击item切换选中状态
    connect(ui->treeWidget_chat, &QTreeWidget::itemChanged, this, [=](QTreeWidgetItem *item, int column) {
        Q_UNUSED(item)
        if (column == 0)
        {
            refreshPreview();
        }
    });
    // 初始化预览
    refreshPreview();
}

ChatExportDialog::~ChatExportDialog()
{
    delete ui;
}

QImage ChatExportDialog::generateImageFromMarkdonwText(const QString &text, int width)
{
    PROFILE_FUNCTION();
    QString html = MarkdownHtmlConverter::toMarkdownHtml(text);

    QTextDocument doc;
    doc.setHtml(html);

    // 设置页面宽度
    int pageWidth = width;
    doc.setTextWidth(pageWidth);

    // 设置目标 DPI
    int targetDpi = 96.0 * imageScaleFactor();// 96 是默认的 DPI

    // 计算缩放因子
    qreal scaleFactor = imageScaleFactor();

    // 计算新的尺寸
    QSize newSize = doc.size().toSize() * scaleFactor;

    QImage image(newSize, QImage::Format_ARGB32);
    image.fill(Qt::white);

    // 设置图像的 DPI
    image.setDotsPerMeterX(targetDpi * 39.37);
    image.setDotsPerMeterY(targetDpi * 39.37);

    QPainter painter(&image);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::TextAntialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);
    painter.scale(scaleFactor, scaleFactor);
    doc.drawContents(&painter);

    // 临时解决方案：因为暗色样式表没有为普通文本设置背景颜色，对话控件中通过设置样式表显示暗色，导致暗色模式下导出的
    // 图片文本背景颜色为白色，所有这里将白色像素转为暗色背景。
    if (CB_SETTING.isDarkTheme())
    {
        // 将白色像素转换为指定颜色
        QColor textBackgoroundColor = QColor(46, 47, 48); // QColor("#2e2f30")
        for (int y = 0; y < image.height(); ++y) {
            for (int x = 0; x < image.width(); ++x) {
                if (image.pixelColor(x, y) == Qt::white) {
                    image.setPixelColor(x, y, textBackgoroundColor);
                }
            }
        }
    }

    return image;
}

void ChatExportDialog::loadChatSession()
{
    QJsonArray msgs = mSession.chatStorage();

    for (int i = 0; i < msgs.size(); i++)
    {
        QJsonObject obj = msgs.at(i).toObject();
        QString role    = obj.value("role").toString();
        QString content = obj.value("content").toString();

        QTreeWidgetItem *item = new QTreeWidgetItem;
        item->setCheckState(0, Qt::Checked);
        ui->treeWidget_chat->addTopLevelItem(item);

        QString showMsg = getShowMessage(content);

        QString text;
        QString prefix;
        if (role == "user")
        {
            text = QString("%1").arg(showMsg);
            prefix = QString("## 用户\n");
            item->setIcon(0, USER_ICON_INFO.icon());
        }
        else if (role == "assistant")
        {
            text = QString("%1").arg(showMsg);
            prefix = QString("## %1\n").arg(mSession.messageSource(i));
            item->setIcon(0, ROBOT_ICON_INFO.icon());
        }
        content.prepend(prefix);

        item->setText(0, text);
        item->setData(0, RawContent, content);
    }
}

QString ChatExportDialog::getShowMessage(const QString &content) const
{
    QString showText = content;
    showText.replace("\n", " ");

    if (showText.length() > 40)
    {
        return showText.left(60) + "... ";
    }
    {
        return showText;
    }
}

void ChatExportDialog::refreshPreview()
{
    // 读取选中的item拼接文本
    mText.clear();
    //mText += QString("# %1\n\n").arg(mSession.title());

    for (int i = 0; i < ui->treeWidget_chat->topLevelItemCount(); i++)
    {
        auto item = ui->treeWidget_chat->topLevelItem(i);
        if (item->checkState(0) != Qt::Checked)
            continue;

        QString content = item->data(0, RawContent).toString();
        mText += content;

        if (i != (ui->treeWidget_chat->topLevelItemCount() - 1))
        {
            mText += "\n\n";
        }
    }

    // 更新当前显示的预览控件
    if (ui->stackedWidget->currentWidget() == ui->page_text)
    {
        ui->textBrowser->setPlainText(mText);
    }
    else if (ui->stackedWidget->currentWidget() == ui->page_image)
    {
        // 启动异步图像生成
        mSpinner->setVisible(true);
        mImageGenerationWatcher.setFuture(QtConcurrent::run(generateImageFromMarkdonwText, mText, exportImageWidth()));

        // mImage = generateImageFromMarkdonwText(mText, exportImageWidth());
        // QImage scaledImage = mImage.scaled(mImage.size().width() / imageScaleFactor(),
        //                                    mImage.size().height() / imageScaleFactor(),
        //                                    Qt::KeepAspectRatioByExpanding,
        //                                    Qt::SmoothTransformation);

        // // 将 QImage 转换为 QPixmap
        // QPixmap pixmap = QPixmap::fromImage(scaledImage);

        // // 将 QPixmap 设置为 QLabel 的图像
        // ui->label_image->setPixmap(pixmap);
    }
}

int ChatExportDialog::exportImageWidth() const
{
    int baseWidth = 500;
    return (baseWidth * ui->spinBox_imageWidth->value());
}

void ChatExportDialog::onSearchLineEditTextChanged(const QString &text)
{
    for (int i = 0; i < ui->treeWidget_chat->topLevelItemCount(); ++i)
    {
        QTreeWidgetItem *item = ui->treeWidget_chat->topLevelItem(i);
        QString rawContent = item->data(0, RawContent).toString();

        if (text.isEmpty()) {
            item->setHidden(false);
        } else {
            item->setHidden(!rawContent.contains(text, Qt::CaseInsensitive));
        }
    }
}

void ChatExportDialog::onSelectAllBtnClicked()
{
    for (int i = 0; i < ui->treeWidget_chat->topLevelItemCount(); ++i) {
        QTreeWidgetItem *item = ui->treeWidget_chat->topLevelItem(i);
        item->setCheckState(0, Qt::Checked);
    }
}

void ChatExportDialog::onDeselectAllBtnClicked()
{
    for (int i = 0; i < ui->treeWidget_chat->topLevelItemCount(); ++i) {
        QTreeWidgetItem *item = ui->treeWidget_chat->topLevelItem(i);
        item->setCheckState(0, Qt::Unchecked);
    }
}

void ChatExportDialog::onCopyBtnClicked()
{
    QClipboard *clipboard = QApplication::clipboard();
    if (ui->stackedWidget->currentWidget() == ui->page_text)
    {
        clipboard->setText(mText);
    }
    else if (ui->stackedWidget->currentWidget() == ui->page_image)
    {
        clipboard->setImage(mImage);
    }
}

void ChatExportDialog::onExportBtnClicked()
{
    QString filter;
    QString extension;
    if (ui->stackedWidget->currentWidget() == ui->page_text) {
        filter = "Markdown Files (*.md)";
        extension = ".md";
    } else if (ui->stackedWidget->currentWidget() == ui->page_image) {
        filter = "PNG Files (*.png)";
        extension = ".png";
    }

    // 构造默认导出文件名：会话标题 + 扩展名
    QString title = mSession.title();
    if (title.isEmpty())
        title = "ChatSession";

    // 替换文件名中的非法字符
    static const QRegularExpression invalidChars(R"([\\/:*?\"<>|])");
    title.replace(invalidChars, "_");

    QString defaultPath = QDir::homePath() + "/" + title + extension;

    QString fileName = QFileDialog::getSaveFileName(this, "Export File", defaultPath, filter);
    bool saved = false;
    if (!fileName.isEmpty()) {
        if (ui->stackedWidget->currentWidget() == ui->page_text) {
            QFile file(fileName);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out << mText;
                file.close();
                saved = true;
            }
        } else if (ui->stackedWidget->currentWidget() == ui->page_image) {
            saved = mImage.save(fileName, "PNG");
        }
    }

    // 如果导出成功，则关闭对话框
    if (saved)
        accept();
}

void ChatExportDialog::handleImageGenerated(const QImage &image)
{
    mSpinner->setVisible(false);

    QImage scaledImage = mImage.scaled(mImage.size().width() / imageScaleFactor(),
                                       mImage.size().height() / imageScaleFactor(),
                                       Qt::KeepAspectRatioByExpanding,
                                       Qt::SmoothTransformation);

    // 将 QImage 转换为 QPixmap
    QPixmap pixmap = QPixmap::fromImage(scaledImage);

    // 将 QPixmap 设置为 QLabel 的图像
    ui->label_image->setPixmap(pixmap);
}

}
