#include "codeboosteroptionspage.h"

#include <QPushButton>
#include <QDesktopServices>
#include <QTimer>

#include <coreplugin/icore.h>
#include <utils/layoutbuilder.h>
#include <coreplugin/minisplitter.h>
#include <utils/utilsicons.h>

#include "codeboosterconstants.h"
#include "codeboostersettings.h"
#include "codeboostertr.h"
#include "codeboostericons.h"
#include "codeboosterutils.h"
#include "chatsidebar/slashcommand/slashcommand.h"

namespace CodeBooster {

class CodeBoosterOptionsPageWidget : public Core::IOptionsPageWidget
{
public:
    CodeBoosterOptionsPageWidget()
    {
        using namespace Layouting;

        QLabel *errorLabel = new QLabel(this);

        QWidget *versionContainer = new QWidget(this);
        {
            QHBoxLayout *versionContainerLy = new QHBoxLayout(versionContainer);
            versionContainerLy->setContentsMargins(0, 0, 0, 0);

            QLabel *versionLabel = new QLabel(this);
            versionLabel->setText(QString("版本：%1").arg(Constants::VERSION));
            versionContainerLy->addWidget(versionLabel);

            QPushButton *getNewVersionButton = new QPushButton(UPGRADE_ICON.icon(), "获取新版本", this);
            connect(getNewVersionButton, &QPushButton::clicked, this, [getNewVersionButton](){
                // 定义要打开的网址
                QUrl url("https://www.yuque.com/alipaycg3vso1nnd/rldsw9/wedollzll6hbvtdk");
                // 使用QDesktopServices打开网址
                QDesktopServices::openUrl(url);
            });
            versionContainerLy->addWidget(getNewVersionButton);

            QPushButton *helpButton = new QPushButton(HELPCENTER_ICON.icon(), "查看帮助文档", this);
            connect(helpButton, &QPushButton::clicked, this, [helpButton](){
                // 定义要打开的网址
                QUrl url("https://www.yuque.com/alipaycg3vso1nnd/rldsw9/yhug9n12blubhq7n");
                // 使用QDesktopServices打开网址
                QDesktopServices::openUrl(url);
            });
            versionContainerLy->addWidget(helpButton);

            QPushButton *updateInfoButton = new QPushButton(UPDATEINFO_ICON.icon(), "查看更新说明", this);
            connect(updateInfoButton, &QPushButton::clicked, this, [updateInfoButton](){
                // 定义要打开的网址
                QUrl url("https://www.yuque.com/alipaycg3vso1nnd/rldsw9/qyag2h5qmi6kly6g");
                // 使用QDesktopServices打开网址
                QDesktopServices::openUrl(url);
            });
            versionContainerLy->addWidget(updateInfoButton);

            // 添加水平弹簧
            versionContainerLy->addStretch(1);
        }

        QWidget *commandContainer = new QWidget(this);
        {
            QHBoxLayout *containerLy = new QHBoxLayout(commandContainer);
            containerLy->setContentsMargins(0, 0, 0, 0);

            QPushButton *openCmdFolder = new QPushButton(Utils::Icons::OPENFILE.icon(), "打开自定义命令文件夹", this);
            connect(openCmdFolder, &QPushButton::clicked, this, [](){
                QString commandsFolderPath = Internal::dataFolderPath() + "/commands";
                QDesktopServices::openUrl(QUrl::fromLocalFile(commandsFolderPath));
            });
            containerLy->addWidget(openCmdFolder);

            QPushButton *reloadCommandBtn = new QPushButton(Utils::Icons::RELOAD.icon(), "重新加载命令", this);
            QLabel *infoLabel = new QLabel(this);
            infoLabel->setStyleSheet("color: #207e0f");

            connect(reloadCommandBtn, &QPushButton::clicked, this, [infoLabel](){
                infoLabel->setText("");
                infoLabel->show();
                int loadedCmdCount = Internal::SlashCommandFactory::instance().reloadCommands();
                infoLabel->setText(QString("加载了 %1 条命令").arg(loadedCmdCount));
                QTimer::singleShot(3000, infoLabel, &QLabel::hide);
            });
            containerLy->addWidget(reloadCommandBtn);
            containerLy->addWidget(infoLabel);

            // 添加水平弹簧
            containerLy->addStretch(1);
        }

        // GitIgnore 规则容器
        QWidget *gitIgnoreContainer = new QWidget(this);
        {
            QHBoxLayout *containerLy = new QHBoxLayout(gitIgnoreContainer);
            containerLy->setContentsMargins(0, 0, 0, 0);

            QPushButton *copyDefaultRulesBtn = new QPushButton(Utils::Icons::COPY.icon(), "复制默认规则", this);
            QLabel *gitIgnoreInfoLabel = new QLabel(this);
            gitIgnoreInfoLabel->setStyleSheet("color: #207e0f");
            gitIgnoreInfoLabel->hide();

            connect(copyDefaultRulesBtn, &QPushButton::clicked, this, [gitIgnoreInfoLabel](){
                try {
                    QString defaultRules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
                    CodeBoosterSettings::instance().gitIgnoreRules.setValue(defaultRules);

                    gitIgnoreInfoLabel->setText("默认规则已复制到输入框");
                    gitIgnoreInfoLabel->show();
                    QTimer::singleShot(3000, gitIgnoreInfoLabel, &QLabel::hide);
                } catch (...) {
                    Internal::outputMessages({"复制默认规则失败"}, Internal::Error);
                }
            });
            containerLy->addWidget(copyDefaultRulesBtn);
            containerLy->addWidget(gitIgnoreInfoLabel);

            // 添加水平弹簧
            containerLy->addStretch(1);
        }

        // ConfigEditorWidget *jsonConfigEditor = new ConfigEditorWidget(this);
        // 可以考虑使用自定义的widget显示，方便高亮json语法，参考clangformat.cpp
        // 布局样式可以参考:screenrecordersettings.cpp
        Column {
            versionContainer,
            hr, br,
            CodeBoosterSettings::instance().autoComplete, br,
            Group {
                title(Tr::tr("配置")),
                Column {
                    CodeBoosterSettings::instance().configJson,
                    errorLabel, br
                },
            },
            Group {
                title(Tr::tr("文件夹上下文过滤规则")),
                Column {
                    CodeBoosterSettings::instance().gitIgnoreRules,
                    gitIgnoreContainer, br
                },
            },
            commandContainer
            ,
        }.attachTo(this);

        setOnApply([] {
            CodeBoosterSettings::instance().apply();
            if (CodeBoosterSettings::instance().applySucess())
            {
                CodeBoosterSettings::instance().writeSettings();

                if (CodeBoosterSettings::instance().needRestart())
                    Core::ICore::askForRestart(Tr::tr("控件样式修改需要重启生效"));
            }
        });

        CodeBoosterSettings::instance().initConfigJsonSetting();

        errorLabel->setVisible(false);
        connect(&CodeBoosterSettings::instance(), &CodeBoosterSettings::showModelConfigErrInfo,
                this, [=](const QStringList &errInfos){
            if (errInfos.isEmpty())
            {
                errorLabel->setVisible(false);
            }
            else
            {
                errorLabel->setVisible(true);
                QString htmlStr = "<b><font color='red'>保存失败，配置格式错误：</font></b><br>";
                for (const QString &err : errInfos)
                {
                    htmlStr += QString("<font color='red'>%1</font><br>").arg(err);
                }
                // 去掉末尾换行
                htmlStr.chop(4);
                errorLabel->setText(htmlStr);
            }
        });
    }
};

CodeBoosterOptionsPage::CodeBoosterOptionsPage()
{
    setId(Constants::CODEBOOSTER_GENERAL_OPTIONS_ID);
    setDisplayName("CodeBooster");
    setCategory(Constants::CODEBOOSTER_GENERAL_OPTIONS_CATEGORY);
    setDisplayCategory(Constants::CODEBOOSTER_GENERAL_OPTIONS_DISPLAY_CATEGORY);
    setCategoryIconPath(":/codebooster/images/settingscategory_codebooster.png");
    setWidgetCreator([] { return new CodeBoosterOptionsPageWidget; });
}

CodeBoosterOptionsPage &CodeBoosterOptionsPage::instance()
{
    static CodeBoosterOptionsPage settingsPage;
    return settingsPage;
}


} // namespace CodeBooster
