# 多个CodeSnippetWidget功能实现说明

## 功能概述

本次实现为CodeBooster插件的InputWidget添加了支持多个CodeSnippetWidget的功能，主要包括：

1. **支持显示多条CodeSnippetWidget**
2. **添加置顶按钮功能**
3. **智能管理置顶和非置顶的代码片段**
4. **显示文件名和行号信息** ✨ 新增功能

## 主要修改

### 1. CodeSnippetWidget类的增强

#### 新增成员变量和方法：
- `bool mIsPinned` - 置顶状态标记
- `QAction *mActionPin` - 置顶按钮
- `int mStartLine, mEndLine` - 代码片段的开始和结束行号
- `bool isPinned() const` - 获取置顶状态
- `void setPinned(bool pinned)` - 设置置顶状态
- `void onActionPinTriggered()` - 置顶按钮槽函数
- `void showCodeSnippet(fileName, text, startLine, endLine)` - 显示代码片段（包含行号）

#### 新增信号：
- `void pinStateChanged(bool pinned)` - 置顶状态变化信号
- `void closeRequested()` - 关闭请求信号

#### 功能特性：
- **智能图标切换**：
  - 未置顶时使用 `ICON_PIN` 图标（普通状态）
  - 置顶时使用 `ICON_PINNED` 图标（激活状态，带颜色高亮）
- 置顶时自动折叠代码片段显示
- 置顶状态下按钮提示文本为"取消置顶"，否则为"置顶"
- **显示行号信息**：标题栏显示 "文件名 (第X行)" 或 "文件名 (第X-Y行)"
- **代码片段文本包含行号**：发送给AI的文本包含行号信息

### 2. InputWidget类的重构

#### 数据结构变更：
- 将单个 `CodeSnippetWidget *mCodeSnippetWgt` 改为 `QList<CodeSnippetWidget*> mCodeSnippetWidgets`
- 添加 `QVBoxLayout *mSnippetContainerLayout` 用于管理多个代码片段控件的布局

#### 核心逻辑实现：

##### `onShowCodeSnippet()` 方法：
- 当编辑器选中文本为空时，清除所有非置顶的代码片段
- 当有置顶代码片段存在时，创建新的代码片段控件显示当前选中内容
- 当没有置顶代码片段时，复用或创建普通代码片段控件

##### `createNewCodeSnippetWidget()` 方法：
- 创建新的CodeSnippetWidget实例
- 连接关闭请求和置顶状态变化信号
- 添加到布局和管理列表中

##### `setShowEditorSelection()` 方法：
- 关闭编辑器选中显示时，只清除非置顶的代码片段
- 保留置顶的代码片段

##### `onSendButtonClicked()` 方法：
- 收集所有可见代码片段的内容
- 发送消息后只清除非置顶的代码片段
- 保留置顶的代码片段继续显示

## 使用场景

### 场景1：普通使用
1. 用户在编辑器中选中代码
2. InputWidget自动显示选中的代码片段
3. 用户可以展开/折叠或关闭代码片段
4. 发送消息后代码片段自动清除

### 场景2：置顶使用
1. 用户在编辑器中选中代码
2. InputWidget显示代码片段
3. 用户点击置顶按钮，代码片段被置顶并自动折叠
4. 用户继续在编辑器中选中其他代码
5. InputWidget创建新的代码片段显示新选中的内容
6. 现在有两个代码片段：一个置顶的，一个当前选中的
7. 发送消息后，只有非置顶的代码片段被清除，置顶的继续保留

### 场景3：多个置顶
1. 用户可以置顶多个代码片段
2. 每次编辑器选中变化时，都会创建新的代码片段控件
3. 置顶的代码片段独立存在，不受编辑器选中状态影响

## 技术细节

### 内存管理
- 使用Qt的父子关系自动管理内存
- 通过`deleteLater()`安全删除控件
- 及时从管理列表中移除已删除的控件

### 信号槽连接
- 每个CodeSnippetWidget的关闭请求信号连接到InputWidget的清理逻辑
- 置顶状态变化信号用于未来可能的扩展功能

### 布局管理
- 使用QVBoxLayout垂直排列多个代码片段控件
- 动态添加和移除控件，自动调整布局

## 新增功能：行号显示

### 信号链路修改
为了支持行号显示，修改了整个信号传递链路：

1. **CodeBoosterClient::documentSelectionChanged** 信号增加行号参数：
   ```cpp
   void documentSelectionChanged(const QString &fileName, const QString &text, int startLine, int endLine);
   ```

2. **计算选中文本行号**：
   - 使用 `QTextCursor::selectionStart()` 和 `selectionEnd()` 获取选中范围
   - 通过 `QTextCursor::blockNumber()` 计算行号（从1开始）

3. **显示格式**：
   - 单行选中：`文件名 (第X行)`
   - 多行选中：`文件名 (第X-Y行)`
   - 发送给AI的文本：`代码 (文件名, 第X行):` 或 `代码 (文件名, 第X-Y行):`

### 修改的文件
- `codeboosterclient.h/cpp` - 信号定义和行号计算
- `codeboosterplugin.h` - 信号转发
- `inputwidget.h/cpp` - 接收和显示行号信息

## 🔧 修复：长文件名显示问题

### 问题描述
当侧边栏宽度不够时，长文件标题会导致整个工具栏（包括按钮）被隐藏。

### 解决方案
1. **文件名标签优化**：
   - 设置 `QSizePolicy::Ignored` 允许标签被压缩
   - 设置 `setMinimumWidth(0)` 允许压缩到最小
   - 禁用自动换行 `setWordWrap(false)`

2. **智能文本省略**：
   - 实现 `updateFileNameDisplay()` 方法
   - 使用 `QFontMetrics::elidedText()` 进行中间省略
   - 动态计算可用宽度，确保按钮始终可见

3. **响应式布局**：
   - 在 `resizeEvent()` 中自动更新文本显示
   - 完整文件名作为 tooltip 显示

### 核心实现
```cpp
// 设置文本省略模式，确保长文件名不会挤压按钮
mFileNameTitle->setSizePolicy(QSizePolicy::Ignored, QSizePolicy::Preferred);
mFileNameTitle->setMinimumWidth(0); // 允许标签被压缩到最小

// 智能计算可用宽度并省略文本
QFontMetrics metrics(mFileNameTitle->font());
QString elidedText = metrics.elidedText(fullText, Qt::ElideMiddle, availableWidth);
```

## 兼容性

本实现完全向后兼容，不影响现有功能：
- 原有的单个代码片段显示逻辑得到保留
- 所有现有的API接口保持不变（只是增加了可选的行号参数）
- 用户界面保持一致的外观和行为
- 行号参数有默认值，确保向后兼容
