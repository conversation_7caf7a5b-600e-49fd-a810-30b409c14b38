#include "codeboostersettings.h"

#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QLoggingCategory>
#include <projectexplorer/project.h>
#include <coreplugin/messagemanager.h>

#include "codeboosterconstants.h"
#include "codeboostertr.h"
#include "codeboosterutils.h"

namespace CodeBooster {

static Q_LOGGING_CATEGORY(codeBooster, "qtc.codebooster.setting", QtWarningMsg)

CodeBoosterSettings::CodeBoosterSettings():
    mMaxAutoCompleteContextTokens(2048)
    , mChatAttachedMessagesCount(0)
    , mDevMode(false)
    , mApplySucess(true)
    , mPredictQuestions(false)
    , mNeedRestartToApply(false)
{
    setAutoApply(false);

    colorScheme.setSettingsKey("CodeBooster.ColorScheme");
    colorScheme.setDisplayStyle(Utils::SelectionAspect::DisplayStyle::ComboBox);
    colorScheme.addOption(Tr::tr("自动"), Tr::tr("跟随 Qt Creator 主题"));
    colorScheme.addOption(Tr::tr("亮色"));
    colorScheme.addOption(Tr::tr("暗色"));
    colorScheme.setDefaultValue(ColorTheme::Auto);
    colorScheme.setLabelText(Tr::tr("控件主题颜色:"));
    colorScheme.setToolTip("重启 Qt Creator 后生效");

    autoComplete.setDisplayName(Tr::tr("Auto Complete"));
    autoComplete.setSettingsKey("CodeBooster.Autocomplete");
    autoComplete.setLabelText(Tr::tr("开启Tab自动补全"));
    autoComplete.setDefaultValue(true);
    autoComplete.setToolTip(Tr::tr("在文档更改后自动请求当前文本光标位置的建议"));

    // 初始化设置
    configJson.setDisplayName(Tr::tr("Model Config"));
    configJson.setSettingsKey("CodeBooster.ConfigJson");
    //configJson.setLabelText(Tr::tr("配置："));
    configJson.setToolTip(Tr::tr("CodeBooster config write here"));
    configJson.setDisplayStyle(Utils::StringAspect::DisplayStyle::TextEditDisplay);
    configJson.setDefaultValue(defaultModelConfig());

    // 初始化 GitIgnore 规则设置
    gitIgnoreRules.setDisplayName(Tr::tr("Folder Context Filter Rules"));
    gitIgnoreRules.setSettingsKey("CodeBooster.GitIgnoreRules");
    gitIgnoreRules.setLabelText(Tr::tr("文件夹上下文过滤规则（.gitignore 格式）："));
    gitIgnoreRules.setToolTip(Tr::tr("使用 .gitignore 格式的规则来过滤文件夹上下文中的文件和目录。\n"
                                     "支持通配符 (*、?)、目录规则 (/)、否定规则 (!) 等。\n"
                                     "留空时使用内置默认规则。"));
    gitIgnoreRules.setDisplayStyle(Utils::StringAspect::DisplayStyle::TextEditDisplay);
    gitIgnoreRules.setDefaultValue(""); // 默认为空，使用内置规则

    readSettings();
    mTheme = ColorTheme(colorScheme.value());

    QStringList errInfos;
    parseConfigSettings(configJson.value().trimmed(), errInfos);

    // 输出错误信息
    for (const QString &errInfo : errInfos)
    {
        // 当不启用自动补全时不提示补全模型的错误信息
        if (!autoComplete() && errInfo.contains("tabAutocompleteModel"))
            continue;
        CodeBooster::Internal::outputMessages({errInfo}, CodeBooster::Internal::Error);
    }
}

CodeBoosterSettings &CodeBoosterSettings::instance()
{
    static CodeBoosterSettings settings;
    return settings;
}

QJsonObject CodeBoosterSettings::buildRequestParamJson(const ModelParam &param, bool stream)
{
    QJsonObject requestJson;

    requestJson.insert("model", param.modelName);
    requestJson.insert("temperature", param.temperature);
    requestJson.insert("top_p", param.top_p);
    requestJson.insert("max_tokens", param.max_tokens);
    requestJson.insert("presence_penalty", param.presence_penalty);
    requestJson.insert("frequency_penalty", param.frequency_penalty);
    requestJson.insert("stream", stream);

    return requestJson;
}

CodeBoosterSettings::ColorTheme CodeBoosterSettings::theme()
{
    return mTheme;
}

bool CodeBoosterSettings::isDarkTheme()
{
    // NOTE: 20241010，QPushButton固定跟随软件样式，屏蔽此功能，默认自动样式
    mTheme = Auto;

    if (theme() == Auto)
    {
        bool darkTheme = Utils::creatorTheme() && Utils::creatorTheme()->flag(Utils::Theme::DarkUserInterface);
        return darkTheme;
    }
    else
    {
        return ( (theme() == Dark) ? true : false );
    }
}

int CodeBoosterSettings::chatAttachedMsgCount() const
{
    if ((mChatAttachedMessagesCount < 99) && (mChatAttachedMessagesCount >= 0))
        return mChatAttachedMessagesCount;

    return 0;
}

bool CodeBoosterSettings::devMode() const
{
    return mDevMode;
}

QString CodeBoosterSettings::getGitIgnoreRules() const
{
    QString rules = gitIgnoreRules.value().trimmed();
    if (rules.isEmpty()) {
        return getDefaultGitIgnoreRules();
    }
    return rules;
}

QString CodeBoosterSettings::getDefaultGitIgnoreRules() const
{
    return QString(
        "# 构建目录\n"
        "build/\n"
        "Build/\n"
        "BUILD/\n"
        "\n"
        "# Qt 用户文件\n"
        "*.user\n"
        "*.user.*\n"
        "\n"
        "# 隐藏文件和目录（以点开头）\n"
        ".*\n"
        "\n"
        "# 常见的临时文件和缓存\n"
        "*.tmp\n"
        "*.temp\n"
        "*.cache\n"
        "*.log\n"
        "\n"
        "# 编译产物\n"
        "*.o\n"
        "*.obj\n"
        "*.exe\n"
        "*.dll\n"
        "*.so\n"
        "*.dylib\n"
        "\n"
        "# IDE 相关\n"
        ".vscode/\n"
        ".idea/\n"
        "*.pro.user\n"
    );
}

QString CodeBoosterSettings::defaultModelConfig()
{
    // TODO: 每个model都可以定义systemmsg，未定义时使用默认systemMsg
    // TODO: 是否添加自动补全延时debounceDelay？
    QString setting =R"(
{
  "tabAutocompleteModel": {
    "model": "",
    "apiKey": "",
    "apiUrl": "",
    "maxOutputTokens": 1024,
    "temperature": 0.1,
    "top_P": 0.7,
    "presence_penalty": 0,
    "frequency_penalty": 0,
    "maxContextTokens": 2048
  },
  "chatModels": [
    {
      "title": "",
      "model": "",
      "apiKey": "",
      "apiUrl": "",
      "maxOutputTokens": 2048,
      "temperature": 0.1,
      "top_P": 0.7,
      "presence_penalty": 0,
      "frequency_penalty": 0
    }
  ],
  "options": {
    "chatAttachedMessagesCount": 99
    "predictQuestions": true
  }
  "codeInstructions":[
    "给下面的函数添加注释",
    "给下面代码添加注释"
  ]
}
)";

    return setting.trimmed();
}

QString CodeBoosterSettings::apiBaseToUrl(const QString &apiBase)
{
    //return QString("https://%1/chat/completions").arg(apiBase);
    return apiBase;
}

ModelParam CodeBoosterSettings::defaultAcmModelParam()
{
    ModelParam param;
    param.temperature = 0.1;
    param.top_p       = 0.7;
    param.max_tokens  = 1024;
    param.presence_penalty = 0;
    param.frequency_penalty = 0;

    return param;
}

ModelParam CodeBoosterSettings::defaultChatModelParam()
{
    ModelParam param;
    param.temperature = 0.1;
    param.top_p       = 0.7;
    param.max_tokens  = 2048;
    param.presence_penalty = 0;
    param.frequency_penalty = 0;

    return param;
}

int CodeBoosterSettings::defaultMaxAutoCompleteContextTokens()
{
    return 2048;
}

void CodeBoosterSettings::apply()
{
    {
        mNeedRestartToApply = false;
    }

    // 记录原始配置
    ModelParam oldAcmParam = mAutoCompModelParam;
    QList<ModelParam> oldChatParams = mChatModelParams;
    int oldMaxAcmContextTokens = mMaxAutoCompleteContextTokens;

    // 初始化
    mAutoCompModelParam = ModelParam();
    mChatModelParams.clear();
    mMaxAutoCompleteContextTokens = 2048;
    mDevMode = false;
    mPredictQuestions = true;
    mCodeInstructions.clear();

    // 读取设置
    AspectContainer::apply();
    QString configJsonStr = configJson.expandedValue();
    QStringList errInfos;

    parseConfigSettings(configJsonStr, errInfos);

    // 验证 GitIgnore 规则
    QString gitIgnoreRulesStr = gitIgnoreRules.value().trimmed();
    if (!gitIgnoreRulesStr.isEmpty()) {
        validateGitIgnoreRules(gitIgnoreRulesStr, errInfos);
    }

    // 没有错误的情况下才应用设置
    if (errInfos.isEmpty())
    {
        mApplySucess = true;

        // 样式修改后需要重启才能生效
        if (mTheme != ColorTheme(colorScheme.value()))
        {
            mNeedRestartToApply = true;
        }

        emit showModelConfigErrInfo(QStringList());
        emit modelConfigUpdated();
    }
    else
    {
        // 还原模型设置
        mAutoCompModelParam = oldAcmParam;
        mChatModelParams    = oldChatParams;
        mMaxAutoCompleteContextTokens = oldMaxAcmContextTokens;

        mApplySucess = false;

        // 显示错误信息
        emit showModelConfigErrInfo(errInfos);
    }
}

void CodeBoosterSettings::initConfigJsonSetting()
{
    if (QString(configJson.value()).trimmed().isEmpty())
    {
        configJson.setValue(defaultModelConfig(), BeQuiet);
    }
}

bool CodeBoosterSettings::applySucess() const
{
    return mApplySucess;
}

bool CodeBoosterSettings::needRestart() const
{
    return mNeedRestartToApply;
}

void CodeBoosterSettings::parseConfigSettings(const QString &configJsonStr, QStringList &errInfos)
{
    // 解析 JSON 字符串
    QJsonDocument jsonDoc = QJsonDocument::fromJson(configJsonStr.toUtf8());
    if (jsonDoc.isObject())
    {
        QJsonObject jsonObj = jsonDoc.object();

        // 解析 tabAutocompleteModel
        if (jsonObj.contains("tabAutocompleteModel") && jsonObj["tabAutocompleteModel"].isObject())
        {
            QJsonObject tabAutocompleteModelObj = jsonObj["tabAutocompleteModel"].toObject();

            // 必须字段
            if (tabAutocompleteModelObj.contains("model"))
                mAutoCompModelParam.modelName = tabAutocompleteModelObj["model"].toString();
            else
                errInfos << "缺失字段：tabAutocompleteModel.model";

            if (tabAutocompleteModelObj.contains("apiKey"))
                mAutoCompModelParam.apiKey = tabAutocompleteModelObj["apiKey"].toString();
            else
                errInfos << "缺失字段：tabAutocompleteModel.apiKey";

            if (tabAutocompleteModelObj.contains("apiUrl"))
                mAutoCompModelParam.apiUrl = apiBaseToUrl(tabAutocompleteModelObj["apiUrl"].toString());
            else
                errInfos << "缺失字段：tabAutocompleteModel.apiBase";

            // 非必须字段
            if (tabAutocompleteModelObj.contains("maxOutputTokens"))
                mAutoCompModelParam.max_tokens = tabAutocompleteModelObj["maxOutputTokens"].toInt();
            else
                mAutoCompModelParam.max_tokens = defaultAcmModelParam().max_tokens;

            if (tabAutocompleteModelObj.contains("temperature"))
                mAutoCompModelParam.temperature = tabAutocompleteModelObj["temperature"].toDouble();
            else
                mAutoCompModelParam.temperature = defaultAcmModelParam().temperature;

            if (tabAutocompleteModelObj.contains("top_P"))
                mAutoCompModelParam.top_p = tabAutocompleteModelObj["top_P"].toDouble();
            else
                mAutoCompModelParam.top_p = defaultAcmModelParam().top_p;

            if (tabAutocompleteModelObj.contains("presence_penalty"))
                mAutoCompModelParam.presence_penalty = tabAutocompleteModelObj["presence_penalty"].toInt();
            else
                mAutoCompModelParam.presence_penalty = defaultAcmModelParam().presence_penalty;

            if (tabAutocompleteModelObj.contains("frequency_penalty"))
                mAutoCompModelParam.frequency_penalty = tabAutocompleteModelObj["frequency_penalty"].toInt();
            else
                mAutoCompModelParam.frequency_penalty = defaultAcmModelParam().frequency_penalty;


            if (tabAutocompleteModelObj.contains("maxContextTokens"))
                mMaxAutoCompleteContextTokens = tabAutocompleteModelObj["maxContextTokens"].toInt();
            else
                mMaxAutoCompleteContextTokens = defaultMaxAutoCompleteContextTokens();
        }
        else
        {
            errInfos << "缺失字段：tabAutocompleteModel";
        }

        // 解析 models
        if (jsonObj.contains("chatModels") && jsonObj["chatModels"].isArray())
        {
            QJsonArray modelsArray = jsonObj["chatModels"].toArray();
            for (const QJsonValue& modelValue : modelsArray)
            {
                if (modelValue.isObject())
                {
                    QJsonObject modelObj = modelValue.toObject();
                    ModelParam modelParam;

                    // 必须字段
                    if (modelObj.contains("title"))
                        modelParam.title = modelObj["title"].toString();
                    else
                        errInfos << "缺失字段：chatModels.title";

                    if (modelObj.contains("model"))
                        modelParam.modelName = modelObj["model"].toString();
                    else
                        errInfos << "缺失字段：chatModels.model";

                    if (modelObj.contains("apiKey"))
                        modelParam.apiKey = modelObj["apiKey"].toString();
                    else
                        errInfos << "缺失字段：chatModels.apiKey";

                    if (modelObj.contains("apiUrl"))
                        modelParam.apiUrl = apiBaseToUrl(modelObj["apiUrl"].toString());
                    else
                        errInfos << "缺失字段：chatModels.apiBase";

                    // 非必须字段
                    if (modelObj.contains("maxOutputTokens"))
                        mAutoCompModelParam.max_tokens = modelObj["maxOutputTokens"].toInt();
                    else
                        mAutoCompModelParam.max_tokens = defaultChatModelParam().max_tokens;

                    if (modelObj.contains("temperature"))
                        mAutoCompModelParam.temperature = modelObj["temperature"].toDouble();
                    else
                        mAutoCompModelParam.temperature = defaultChatModelParam().temperature;

                    if (modelObj.contains("top_P"))
                        mAutoCompModelParam.top_p = modelObj["top_P"].toDouble();
                    else
                        mAutoCompModelParam.top_p = defaultChatModelParam().top_p;

                    if (modelObj.contains("presence_penalty"))
                        mAutoCompModelParam.presence_penalty = modelObj["presence_penalty"].toInt();
                    else
                        mAutoCompModelParam.presence_penalty = defaultChatModelParam().presence_penalty;

                    if (modelObj.contains("frequency_penalty"))
                        mAutoCompModelParam.frequency_penalty = modelObj["frequency_penalty"].toInt();
                    else
                        mAutoCompModelParam.frequency_penalty = defaultChatModelParam().frequency_penalty;

                    mChatModelParams.append(modelParam);
                }
            }
        }
        else
        {
            errInfos << "缺失字段：chatModels";
        }

        // 解析options
        if (jsonObj.contains("options") && jsonObj["options"].isObject())
        {
            QJsonObject optionsObj = jsonObj["options"].toObject();

            if (optionsObj.contains("chatAttachedMessagesCount"))
            {
                mChatAttachedMessagesCount = optionsObj["chatAttachedMessagesCount"].toInt();
            }

            if (optionsObj.contains("devMode"))
            {
                mDevMode = optionsObj["devMode"].toBool();
            }

            if (optionsObj.contains("predictQuestions"))
            {
                mPredictQuestions = optionsObj["predictQuestions"].toBool();
            }
        }
        else
        {
            errInfos << "缺失字段：options";
        }

        // 解析codeInstructions（非必须）
        if (jsonObj.contains("codeInstructions"))
        {
            if (jsonObj["codeInstructions"].isArray())
            {
                QJsonArray instArray = jsonObj["codeInstructions"].toArray();
                for (const QJsonValue& instText : instArray)
                {
                    if (instText.isString())
                    {
                        QString text = instText.toString();
                        if (!text.isEmpty())
                            mCodeInstructions << instText.toString();
                    }
                }
            }
            else
            {
                errInfos << "codeInstructions 字段格式错误";
            }
        }
    }
    else
    {
        errInfos << QString("JSON 不是一个对象");
    }
}

void CodeBoosterSettings::validateGitIgnoreRules(const QString &rules, QStringList &errInfos)
{
    if (rules.trimmed().isEmpty()) {
        return; // 空规则是有效的，将使用默认规则
    }

    QStringList lines = rules.split('\n', Qt::SkipEmptyParts);
    int lineNumber = 0;

    for (const QString &line : lines) {
        lineNumber++;
        QString trimmedLine = line.trimmed();

        // 跳过空行和注释行
        if (trimmedLine.isEmpty() || trimmedLine.startsWith('#')) {
            continue;
        }

        // 基本的规则验证
        try {
            QString pattern = trimmedLine;

            // 移除否定前缀
            if (pattern.startsWith('!')) {
                pattern = pattern.mid(1);
            }

            // 移除目录后缀
            if (pattern.endsWith('/')) {
                pattern = pattern.left(pattern.length() - 1);
            }

            // 检查是否包含无效字符
            if (pattern.contains("**") && !pattern.contains("**/") && !pattern.endsWith("**")) {
                errInfos << QString("第 %1 行：无效的通配符模式 '**'，应使用 '**/' 或以 '**' 结尾").arg(lineNumber);
                continue;
            }

            // 尝试创建正则表达式来验证模式
            QString regexPattern = QRegularExpression::escape(pattern);
            regexPattern.replace("\\*", ".*");
            regexPattern.replace("\\?", ".");

            QRegularExpression regex(regexPattern);
            if (!regex.isValid()) {
                errInfos << QString("第 %1 行：无效的模式 '%2'").arg(lineNumber).arg(trimmedLine);
            }

        } catch (...) {
            errInfos << QString("第 %1 行：解析规则时发生错误 '%2'").arg(lineNumber).arg(trimmedLine);
        }
    }

    // 如果有错误，添加总结信息
    if (!errInfos.isEmpty()) {
        errInfos.prepend("GitIgnore 规则验证失败：");
    }
}

CodeBoosterProjectSettings::CodeBoosterProjectSettings(ProjectExplorer::Project *project, QObject *parent)
{
    setAutoApply(true);

    useGlobalSettings.setSettingsKey(Constants::CODEBOOSTER_USE_GLOBAL_SETTINGS);
    useGlobalSettings.setDefaultValue(true);

    enableAutoComplete.setSettingsKey("CodeBooster.Autocomplete");
    enableAutoComplete.setDisplayName(Tr::tr("Enable CodeBooster AutoComplete"));
    enableAutoComplete.setLabelText(Tr::tr("Enable CodeBooster AutoComplete"));
    enableAutoComplete.setToolTip(Tr::tr("Enables the CodeBooster Intergation AutoComplete."));
    enableAutoComplete.setDefaultValue(false);

    Utils::Store map = Utils::storeFromVariant(project->namedSettings(Constants::CODEBOOSTER_PROJECT_SETTINGS_ID).toMap());
    fromMap(map);

    connect(&enableAutoComplete, &Utils::BoolAspect::changed, this, [this, project] { save(project); });
    connect(&useGlobalSettings, &Utils::BoolAspect::changed, this, [this, project] { save(project); });
}

void CodeBoosterProjectSettings::save(ProjectExplorer::Project *project)
{
    Utils::Store map;
    toMap(map);
    project->setNamedSettings(Constants::CODEBOOSTER_PROJECT_SETTINGS_ID, variantFromStore(map));

    // This triggers a restart of the Copilot language server.
    CodeBoosterSettings::instance().apply();
}

void CodeBoosterProjectSettings::setUseGlobalSettings(bool useGlobal)
{
    useGlobalSettings.setValue(useGlobal);
}

bool CodeBoosterProjectSettings::isEnabled() const
{
    if (useGlobalSettings())
        return CodeBoosterSettings::instance().autoComplete();
    return enableAutoComplete();
}

} // namespace CodeBooster
