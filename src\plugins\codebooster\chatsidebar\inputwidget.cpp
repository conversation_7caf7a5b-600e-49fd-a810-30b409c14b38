#include "inputwidget.h"

#include <QTextBlock>
#include <QDebug>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QRegularExpression>
#include <QScrollBar>
#include <QCheckBox>
#include <QMimeData>
#include <QTimer>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>

#include <utils/utilsicons.h>
#include <texteditor/texteditor.h>
#include <utils/stylehelper.h>
#include <coreplugin/editormanager/editormanager.h>

#include "markdownpreview/notepreviewwidget.h"
#include "markdownpreview/markdownhtmlconverter.h"
#include "customlinewidget.h"
#include "codeboostericons.h"
#include "widgettheme.h"
#include "codeboosterplugin.h"
#include "codeboostersettings.h"
#include "codeboosterutils.h"
#include "useroptions.h"
#include "contextitemwidget.h"

using namespace TextEditor;


namespace CodeBooster::Internal{

/********************************************************************
 CodeSnippetWidget
 代码段显示控件
*********************************************************************/
CodeSnippetWidget::CodeSnippetWidget(QWidget *parent) : QFrame(parent), mIsPinned(false), mStartLine(1), mEndLine(1)
{
    this->setObjectName("CodeSnippetWidget");
    this->setStyleSheet(CB_THEME.SS_InputWidget_CodeSnippet);

    QVBoxLayout *innerLayout = new QVBoxLayout(this);
    innerLayout->setSpacing(0);
    innerLayout->setContentsMargins(0, 0, 0, 0);

    // 初始化工具栏
    {
        mToolBar = new QToolBar(this);
        mToolBar->setObjectName("mToolBar");
        mToolBar->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar);
        mToolBar->installEventFilter(this);

        mFileIcon = new QLabel(this);
        mFileIcon->setFixedWidth(24);
        mFileIcon->setAlignment(Qt::AlignCenter);
        mFileIcon->setPixmap(CODEFILE_ICON.icon().pixmap(QSize(16, 16)));
        mToolBar->addWidget(mFileIcon);

        // 添加文件名称标签
        mFileNameTitle = new QLabel(this);
        mFileNameTitle->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar_Label);
        // 设置最大宽度，防止长文件名挤压按钮
        mFileNameTitle->setMaximumWidth(200);
        mFileNameTitle->setMinimumWidth(50);
        mFileNameTitle->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
        mToolBar->addWidget(mFileNameTitle);

        // 添加弹簧（让按钮靠右对齐）
        QWidget *spacer = new QWidget();
        spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
        mToolBar->addWidget(spacer);

        // 添加置顶按钮
        mActionPin = new QAction(ICON_PIN.icon(), "置顶", this);
        mActionPin->setCheckable(true);
        connect(mActionPin, &QAction::triggered, this, &CodeSnippetWidget::onActionPinTriggered);
        mToolBar->addAction(mActionPin);

        mActionClose = new QAction(Utils::Icons::CLOSE_TOOLBAR.icon(), "关闭", this);
        connect(mActionClose, &QAction::triggered, this, &CodeSnippetWidget::onActionCloseTriggered);
        mToolBar->addAction(mActionClose);

        mActionExpand = new QAction(EXPAND_ICON.icon(), "折叠", this);
        connect(mActionExpand, &QAction::triggered, this, &CodeSnippetWidget::onActionExpandTriggered);
        mToolBar->addAction(mActionExpand);

        innerLayout->addWidget(mToolBar);
    }

    {
        mHorLine = new CustomLineWidget(this, 12);
        innerLayout->addWidget(mHorLine);
    }

    {
        mPreviewWgt = new NotePreviewWidget(this);
        mPreviewWgt->setObjectName("mPreviewWgt");
        mPreviewWgt->disableLineWrap();
        mPreviewWgt->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeMode_PreWgt);

        mPreviewWgt->setHeightMode(NotePreviewWidget::MaxLimit);
        innerLayout->addWidget(mPreviewWgt, 1);
    }

    setMaximumHeight(330);

    this->setLayout(innerLayout);
}

void CodeSnippetWidget::showCodeSnippet(const QString &fileName, const QString &selectedText, int startLine, int endLine)
{
    QString snippet = selectedText;
    snippet.replace(QChar(0x2028), "\n"); // 替换行分隔符 (LS)
    snippet.replace(QChar(0x2029), "\n"); // 替换段落分隔符 (PS)
    snippet = snippet.trimmed();
    if (snippet.isEmpty())
    {
        clear();
        return;
    }

    this->setVisible(true);

    mFileName = fileName;
    mCodeSnippet = snippet;
    mStartLine = startLine;
    mEndLine = endLine;

    // 更新文件名显示（包含省略处理）
    updateFileNameDisplay();

    QString language = languageFromFileSuffix(QString(fileName.section(".", -1)));
    QString codeSnippet = QString("```%1\n%2\n```").arg(language).arg(mCodeSnippet);
    QString htmlStr = MarkdownHtmlConverter::toMarkdownHtml(codeSnippet);
    mPreviewWgt->setHtml(htmlStr);
    if (mPreviewWgt->verticalScrollBar()->isVisible())
    {
        // TODO: 是否可以判断鼠标指针移动的方向来调整滚动条移动到最上方还是最下方？
        mPreviewWgt->verticalScrollBar()->setValue(mPreviewWgt->verticalScrollBar()->maximum());
    }

    mActionExpand->setText("展开");
    onActionExpandTriggered();
}

QString CodeSnippetWidget::codeSnippet() const
{
    if (mCodeSnippet.isEmpty())
        return QString();

    QString codeText;
    // 在代码片段中包含行号信息
    if (mStartLine == mEndLine) {
        codeText += QString("代码 (%1, 第%2行):\n").arg(mFileName).arg(mStartLine);
    } else {
        codeText += QString("代码 (%1, 第%2-%3行):\n").arg(mFileName).arg(mStartLine).arg(mEndLine);
    }
    codeText += QString("```%1\n").arg(languageFromFileSuffix(QString(mFileName.section(".", -1))));
    codeText += mCodeSnippet;
    codeText += QString("\n```");

    return codeText;
}

void CodeSnippetWidget::clear()
{
    mFileName.clear();
    mCodeSnippet.clear();
    mStartLine = 1;
    mEndLine = 1;
    mPreviewWgt->clear();
    mPreviewWgt->setHtml(QString());
    this->setVisible(false);
}

void CodeSnippetWidget::resizeEvent(QResizeEvent *event)
{
    QFrame::resizeEvent(event);
    emit heightChanged(height());
}

void CodeSnippetWidget::updateFileNameDisplay()
{
    if (mFileName.isEmpty())
        return;

    // 构建完整的显示文本
    QString fullText;
    if (mStartLine == mEndLine) {
        fullText = QString("%1 (第%2行)").arg(mFileName).arg(mStartLine);
    } else {
        fullText = QString("%1 (第%2-%3行)").arg(mFileName).arg(mStartLine).arg(mEndLine);
    }

    // 使用QFontMetrics进行简单的文本省略
    QFontMetrics metrics(mFileNameTitle->font());
    int maxWidth = mFileNameTitle->maximumWidth();
    QString displayText = metrics.elidedText(fullText, Qt::ElideMiddle, maxWidth);

    // 设置文本和提示
    mFileNameTitle->setText(displayText);
    mFileNameTitle->setToolTip(fullText);
}

bool CodeSnippetWidget::eventFilter(QObject *watched, QEvent *event)
{
    if (watched == mToolBar)
    {
        if (event->type() == QEvent::HoverEnter)
        {
            this->setCursor(Qt::PointingHandCursor);
            mToolBar->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar_Highlight);
        }
        else if (event->type() == QEvent::HoverLeave)
        {
            mToolBar->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar);
        }
        else if (event->type() == QEvent::MouseButtonPress)
        {
            onActionExpandTriggered();
            return true;
        }
    }
    return QFrame::eventFilter(watched, event);
}

void CodeSnippetWidget::setPinned(bool pinned)
{
    if (mIsPinned == pinned)
        return;

    mIsPinned = pinned;
    mActionPin->setChecked(pinned);

    if (pinned) {
        // 置顶时自动折叠
        mActionExpand->setText("展开");
        mActionExpand->setIcon(COLLAPSE_ICON.icon());
        mHorLine->setVisible(false);
        mPreviewWgt->setVisible(false);

        // 更新置顶按钮图标和提示文本
        mActionPin->setIcon(ICON_PINNED.icon());
        mActionPin->setToolTip("取消置顶");
    } else {
        // 更新置顶按钮图标和提示文本
        mActionPin->setIcon(ICON_PIN.icon());
        mActionPin->setToolTip("置顶");
    }

    emit pinStateChanged(pinned);
}

void CodeSnippetWidget::onActionCloseTriggered()
{
    emit closeRequested();
}

void CodeSnippetWidget::onActionExpandTriggered()
{
    if (mActionExpand->text() == "展开")
    {
        mActionExpand->setText("折叠");
        mActionExpand->setIcon(EXPAND_ICON.icon());

        mHorLine->setVisible(true);
        mPreviewWgt->setVisible(true);
    }
    else
    {
        mActionExpand->setText("展开");
        mActionExpand->setIcon(COLLAPSE_ICON.icon());

        mHorLine->setVisible(false);
        mPreviewWgt->setVisible(false);
    }
}

void CodeSnippetWidget::onActionPinTriggered()
{
    setPinned(!mIsPinned);
}

/********************************************************************
 CustomTextEdit
 自定义输入控件
*********************************************************************/
CustomTextEdit::CustomTextEdit(QWidget *parent) :
    QPlainTextEdit(parent)
    , mFocusMode(false)
{
    // 初始化配色方案
    {
        if (CB_SETTING.isDarkTheme())
        {
            mColorScheme.highlightLineBackground = QColor("#ff764d");
            mColorScheme.lineNumberAreaBackground = QColor("#222222");
            mColorScheme.lineNumberText = QColor("#666666");
            mColorScheme.topSeparator = QColor("#222222");
        }
        else
        {
            mColorScheme.highlightLineBackground = QColor("#ff764d");
            mColorScheme.lineNumberAreaBackground = QColor("#e0e0e0");
            mColorScheme.lineNumberText = QColor("#909090");
            mColorScheme.topSeparator = QColor("#e0e0e0");

            QPalette palette = this->palette();
            palette.setColor(QPalette::PlaceholderText, QColor("#909090"));
            this->setPalette(palette);
        }

        mColorScheme.highlightLineBackground.setAlpha(70);
    }

    // 初始化输入框高度相关设置
    {
        mMinInputHeight = 52;
        mMaxInputHeight = 400;

        setMaximumHeight(mMinInputHeight);
        connect(this, &CustomTextEdit::textChanged, this, &CustomTextEdit::adjustInputEditSize);
        // connect(this, &CustomTextEdit::textChanged, this, [this](){
        //     if (toPlainText().isEmpty())
        //     {
        //         setPlaceholderTextVisible(true);
        //     }
        // });
        connect(this, &CustomTextEdit::sizeChanged, this, &CustomTextEdit::adjustInputEditSize);
    }

    // 初始化行号区域控件
    {
        lineNumberArea = new LineNumberArea(this);

        connect(this, SIGNAL(blockCountChanged(int)), this, SLOT(updateLineNumberAreaWidth(int)));
        connect(this, SIGNAL(updateRequest(QRect,int)), this, SLOT(updateLineNumberArea(QRect,int)));
        connect(this, &CustomTextEdit::cursorPositionChanged, this, [this](){
            highlightCurrentLine(mFocusMode);
        });

        // 默认不开启专注模式
        setFocusMode(false);
    }

    // 文本输入框不显示边框
    setStyleSheet(QString("QPlainTextEdit { border: none; background-color: %1; }").arg(CB_THEME.Color_InputTextBackground));

    // 设置占位文本
    setPlaceholderTextVisible(true);
}

void CustomTextEdit::setFocusMode(bool enable)
{
    mFocusMode = enable;

    lineNumberArea->setVisible(enable);
    highlightCurrentLine(enable);
    updateLineNumberAreaWidth();

    mMaxInputHeight = enable ? 1000 : 400;
    adjustInputEditSize();

    setPlaceholderTextVisible(true);
}

void CustomTextEdit::lineNumberAreaPaintEvent(QPaintEvent *event)
{
    QPainter painter(lineNumberArea);
    painter.fillRect(event->rect(), mColorScheme.lineNumberAreaBackground);

    // 绘制横线，横跨行号区域和编辑器区域
    if (mFocusMode)
    {
        QPen pen(mColorScheme.topSeparator);
        pen.setWidth(6);
        painter.setPen(pen);
        painter.drawLine(0, 0, width(), 0);
    }

    QTextBlock block = firstVisibleBlock();
    int blockNumber = block.blockNumber();
    int top = (int) blockBoundingGeometry(block).translated(contentOffset()).top();
    int bottom = top + (int) blockBoundingRect(block).height();

    QTextCursor cursor = textCursor();
    int currentBlockNumber = cursor.blockNumber();

    while (block.isValid() && top <= event->rect().bottom()) {
        if (block.isVisible() && bottom >= event->rect().top()) {
            QString number = QString::number(blockNumber + 1);
            painter.setPen(mColorScheme.lineNumberText);
            painter.drawText(0, top, lineNumberArea->width(), fontMetrics().height(),
                             Qt::AlignRight, number);

            // 在当前行号区域绘制高亮行颜色
            if (blockNumber == currentBlockNumber)
            {
                painter.fillRect(0, top, lineNumberArea->width(), fontMetrics().height() + 1, mColorScheme.highlightLineBackground);
            }
        }

        block = block.next();
        top = bottom;
        bottom = top + (int) blockBoundingRect(block).height();
        ++blockNumber;
    }
}

int CustomTextEdit::lineNumberAreaWidth()
{
    int digits = 1;
    int max = qMax(1, blockCount());
    while (max >= 10) {
        max /= 10;
        ++digits;
    }

    int space = 3 + fontMetrics().horizontalAdvance(QLatin1Char('9')) * digits;

    return space;
}

void CustomTextEdit::removeContextTag(const ContextItem &context)
{
    QString text = toPlainText();
    QString simpleTag = QString(context.tagText()).trimmed();
    text.remove(simpleTag);
    text.remove(context.tagText());

    setPlainText(text);
}

bool CustomTextEdit::event(QEvent *event)
{
    // FIXME: 使用输入法输入时占位文本不会自动消失
    // if (event->type() == QEvent::InputMethod)
    // {
    //     qDebug() << Q_FUNC_INFO << toPlainText();
    //     setPlaceholderTextVisible(false);
    // }

    if (event->type() == QEvent::ShortcutOverride)
    {
        QKeyEvent *ke = static_cast<QKeyEvent *>(event);
        if (ke->modifiers().testFlag(Qt::ControlModifier))
        {
            if (ke->key() == Qt::Key_N)
            {
                emit newChat();

                event->accept();
                return true;
            }
        }
    }

    return QPlainTextEdit::event(event);
}

void CustomTextEdit::paintEvent(QPaintEvent *event)
{
    QPlainTextEdit::paintEvent(event);

    QPainter painter(viewport());
    painter.setPen(Qt::black);

    // 获取行号区域的宽度
    int lineNumberAreaWidth = this->lineNumberAreaWidth();

    // 绘制横线，横跨行号区域和编辑器区域
    if (mFocusMode)
    {
        QPen pen(mColorScheme.topSeparator);
        pen.setWidth(6);
        painter.setPen(pen);
        painter.drawLine(0, 0, width(), 0);

        QTextCursor cursor = textCursor();
        QTextBlock block = cursor.block();
        int top = (int) blockBoundingGeometry(block).translated(contentOffset()).top();

        // 在行号和当前行高亮区域之间绘制高亮颜色
        painter.fillRect(0, top, 4, fontMetrics().height() + 1, mColorScheme.highlightLineBackground);
    }
}

void CustomTextEdit::resizeEvent(QResizeEvent *event)
{
    QPlainTextEdit::resizeEvent(event);

    QRect cr = contentsRect();
    lineNumberArea->setGeometry(QRect(cr.left(), cr.top(), lineNumberAreaWidth(), cr.height()));
}

void CustomTextEdit::focusInEvent(QFocusEvent *event)
{
    QPlainTextEdit::focusInEvent(event);
    emit focusChange(true);
}

void CustomTextEdit::focusOutEvent(QFocusEvent *event)
{
    QPlainTextEdit::focusOutEvent(event);
    emit focusChange(false);
}

void CustomTextEdit::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Return)
    {
        if (mFocusMode)
        {
            if (event->modifiers() == Qt::NoModifier)
            {
                insertPlainText("\n");
            }
            else if (event->modifiers() == Qt::ControlModifier)
            {
                emit sendMessage();
            }
        }
        else
        {
            if (event->modifiers() == Qt::ShiftModifier)
            {
                // 处理 Shift+Enter 组合键
                insertPlainText("\n");
            }
            else if (event->modifiers() == Qt::NoModifier)
            {
                emit sendMessage();
            }
        }

        return;
    }
    else if (event->key() == Qt::Key_F)
    {
        if (event->modifiers() == Qt::ControlModifier)
        {
            emit focusModeShortcutPress();
            return;
        }
    }

    // 调用基类的 keyPressEvent 处理其他按键事件
    QPlainTextEdit::keyPressEvent(event);
}

void CustomTextEdit::dragEnterEvent(QDragEnterEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls())
    {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists())
            {
                // 如果是文件夹，接受拖拽（文件夹内的非文本文件会在处理时被跳过）
                if (fileInfo.isDir())
                {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile())
                {
                    if (fileIsTextFile(path))
                    {
                        hasValidFiles = true;
                    }
                    else
                    {
                        // 发现非文本文件，拒绝整个拖拽操作
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles)
        {
            event->acceptProposedAction();
        }
        else
        {
            event->ignore();
        }
    }
    else
    {
        // 如果不是URL数据，调用基类处理
        QPlainTextEdit::dragEnterEvent(event);
    }
}

void CustomTextEdit::dragMoveEvent(QDragMoveEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls())
    {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists())
            {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir())
                {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile())
                {
                    if (fileIsTextFile(path))
                    {
                        hasValidFiles = true;
                    }
                    else
                    {
                        // 发现非文本文件，拒绝拖拽
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles)
        {
            event->acceptProposedAction();
        }
        else
        {
            event->ignore();
        }
    }
    else
    {
        // 如果不是URL数据，调用基类处理
        QPlainTextEdit::dragMoveEvent(event);
    }
}

void CustomTextEdit::dropEvent(QDropEvent *event)
{
    // 获取拖拽进来的数据
    const QMimeData *mimeData = event->mimeData();

    // 如果拖拽的数据是URL格式，先检查是否包含非文本文件
    if (mimeData->hasUrls())
    {
        QList<QUrl> urlList = mimeData->urls();
        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists() && fileInfo.isFile())
            {
                // 检查文件是否为文本文件，如果不是则拒绝整个拖拽操作
                if (!fileIsTextFile(path))
                {
                    // 拒绝拖拽事件
                    event->ignore();
                    return;
                }
            }
        }
    }

    // 解决拖拽后光标不刷新的问题：https://www.qtcentre.org/threads/16935-Cursor-stops-being-redrawn-when-QTextEdit-dropEvent()-overrided
    this->setReadOnly(true);
    QPlainTextEdit::dropEvent(event);
    this->setReadOnly(false);

    // 获取拖放的位置
    QPoint dropPosition = event->pos();
    // 将拖放位置转换为文档中的位置
    QTextCursor cursor = cursorForPosition(dropPosition);
    this->setTextCursor(cursor);

    // 打印mimedata详细信息
    // qDebug() << "MimeData formats:" << mimeData->formats();
    // for (const QString &format : mimeData->formats()) {
    //     qDebug() << "Format:" << format;
    //     qDebug() << "Data:" << mimeData->data(format);
    // }

    // 处理拖拽的URL数据
    if (mimeData->hasUrls())
    {
        QList<QUrl> urlList = mimeData->urls();
        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists())
            {
                // 拖进来文件
                if (fileInfo.isFile())
                {
                    // 此时已经确认是文本文件，直接处理
                    bool success = true;
                    QString content = readTextFile(path, success);
                    if (!success)
                    {
                        // todo: 输出日志
                        continue;
                    }

                    ContextItem item;
                    item.name = fileInfo.fileName();
                    item.description = fileInfo.path();
                    item.content = content;
                    item.type = ContextItem::File;
                    item.uri = path;

                    addContextItem(item);
                }
                // 拖进来文件夹
                else if (fileInfo.isDir())
                {
                    QString content;
                    processDirectory(path, content); // 调用递归处理函数

                    ContextItem item;
                    item.name = fileInfo.fileName();
                    item.description = fileInfo.path();
                    item.content = content;
                    item.type = ContextItem::Folder;
                    item.uri = path;

                    addContextItem(item);
                }
            }
        }
    }

    // 接受拖拽事件
    event->accept();
}


void CustomTextEdit::setPlaceholderTextVisible(bool visible)
{
    if (!visible)
    {
        setPlaceholderText(QString());
        return;
    }

    QString text;
    if (mFocusMode)
    {
        text = QString("Ctrl+Enter 发送，Enter 换行，Ctrl+N 创建新对话，Ctrl+F 退出专注模式");
    }
    else
    {
        text = QString("Enter 发送，Shift+Enter 换行，Ctrl+N 创建新对话，Ctrl+F 进入专注模式");
    }

    setPlaceholderText(text);
}

void CustomTextEdit::addContextItem(const ContextItem &item)
{
    this->insertPlainText(item.tagText());
    emit newContextItem(item);
}

// 递归处理文件夹的函数
void CustomTextEdit::processDirectory(const QString &path, QString &content)
{
    QDir dir(path);
    QStringList entryList = dir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    for (const QString &entry : entryList)
    {
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);

        if (fileInfo.isFile()) {
            if (!fileIsTextFile(fullPath)) {
                continue;
            }

            bool success = true;
            QString fileContent = readTextFile(fullPath, success);
            if (!success) {
                // todo: 输出日志
                qDebug() << "Failed to read file:" << fullPath;
                continue;
            }

            content += fileContent + "\n\n";
        } else if (fileInfo.isDir()) {
            processDirectory(fullPath, content); // 递归处理子文件夹
        }
    }
}

void CustomTextEdit::adjustInputEditSize()
{
    int oldHeight = height();
    int newHeight = oldHeight;

    if (mFocusMode)
    {
        setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

        // 防止重复触发此槽函数
        blockSignals(true);
        setMaximumHeight(1000);
        blockSignals(false);
    }
    else
    {
        QTextDocument *doc = document();
        QFontMetrics fm(font());
        int contentHeight = doc->size().height() * fm.lineSpacing();

        // 计算实际显示的行数
        int lineCount = 0;
        for (QTextBlock block = doc->begin(); block != doc->end(); block = block.next()) {
            QTextLayout *layout = block.layout();
            lineCount += layout->lineCount();
        }
        // 当文本行数大于1行时,为了防止文本末尾的空行导致显示进度条，将文本内容高度加上一个文本高度，
        // 将空行显示在输入控件内部，禁止进度条的显示
        if (lineCount >= 2)
        {
            contentHeight += fm.height();
        }

        // 控件高度固定比文本高度高一定的高度，数值是手动调整到合适的高度得到的
        int widgetHeight = contentHeight + 12;

        newHeight = qMin(widgetHeight, mMaxInputHeight);
        newHeight = qMax(mMinInputHeight,newHeight );

        if (newHeight <= mMinInputHeight) {
            setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
        } else {
            setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        }

        // 防止重复触发此槽函数
        blockSignals(true);
        setFixedHeight(newHeight);
        blockSignals(false);
    }

    if (newHeight != oldHeight)
        emit heightChanged(newHeight);
}

void CustomTextEdit::updateLineNumberAreaWidth(int newBlockCount)
{
    Q_UNUSED(newBlockCount)

    int width = mFocusMode ? lineNumberAreaWidth() : 0;
    setViewportMargins(width, 0, 0, 0);
}

void CustomTextEdit::highlightCurrentLine(bool highlight)
{
    QList<QTextEdit::ExtraSelection> extraSelections;

    if (highlight && !isReadOnly()) {
        QTextEdit::ExtraSelection selection;

        selection.format.setBackground(mColorScheme.highlightLineBackground);
        selection.format.setProperty(QTextFormat::FullWidthSelection, true);
        selection.cursor = textCursor();
        selection.cursor.clearSelection();
        extraSelections.append(selection);
    }

    setExtraSelections(extraSelections);
}

void CustomTextEdit::updateLineNumberArea(const QRect &rect, int dy)
{
    if (dy)
        lineNumberArea->scroll(0, dy);
    else
        lineNumberArea->update(0, rect.y(), lineNumberArea->width(), rect.height());

    if (rect.contains(viewport()->rect()))
        updateLineNumberAreaWidth(0);
}

/***************************************************************************************************
 * @brief InputWidget::InputWidget
 * @param parent
 ****************************************************************************************************/
InputWidget::InputWidget(QWidget *parent) : QFrame(parent)
    , mMainInputContainer(new QFrame(this))
    , mShowSnippet(USER_OP.showEditorSelection)
    , mInStreaming(false)
    , mToolBar(nullptr)
    , mFocusEditorAction(nullptr)
{
    setObjectName("InputWidget");
    setAcceptDrops(true);

    //this->setStyleSheet("QFrame#InputWidget {border: 1px solid red; background-color: green;}");

    // 获取当前的背景颜色
    {
        QPalette pal = palette();
        QColor bgColor = pal.color(QPalette::Base);
        mBgColorStr = bgColor.name();
    }

    // 初始化顶层布局
    QVBoxLayout *topLayout = new QVBoxLayout(this);
    topLayout->setContentsMargins(0, 0, 0, 0);
    topLayout->setSpacing(0);

    // 初始化顶部工具栏
    {
        mToolBar = new QToolBar(this);
        mToolBar->setObjectName("mToolBar");
        mToolBar->setIconSize({12, 12});
        mToolBar->setStyleSheet(CB_THEME.SS_InputWidget_ToolBar);

        QVBoxLayout *containerLayout = new QVBoxLayout();
        containerLayout->addWidget(mToolBar);

        containerLayout->setContentsMargins(0, 0, 0, 0);
        topLayout->addLayout(containerLayout, 0);

        mUserCurrentFileAction = new QAction(Utils::Icons::PLUS_TOOLBAR.icon(), "使用当前文件", this);
        connect(mUserCurrentFileAction, &QAction::triggered, this, &InputWidget::onUseCurrentFileClicked);
        mToolBar->addAction(mUserCurrentFileAction);

        {
            // TODO: 自动更新当前文件名称
            QLabel *currentFileLabel = new QLabel(this);
            currentFileLabel->setText("使用当前文件");
            mToolBar->addWidget(currentFileLabel);
        }

        mFocusEditorAction = new QAction(ENLARGE_ICON.icon(), "扩大", this);
        mFocusEditorAction->setCheckable(true);
        mFocusEditorAction->setChecked(false);
        connect(mFocusEditorAction, &QAction::triggered, this, [=](bool checked){
            inputFocusModeChanged(checked);
            mTextEdit->setFocusMode(checked);
        });
        QWidget *spacer = new QWidget();
        spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

        mToolBar->addWidget(spacer);
        mToolBar->addAction(mFocusEditorAction);
    }

    // 初输入区域布局控件
    mMainInputContainer->setObjectName("mMainInputContainer");
    topLayout->addWidget(mMainInputContainer, 1);
    QVBoxLayout *inputLayout = new QVBoxLayout(mMainInputContainer);
    inputLayout->setContentsMargins(0, 0, 0, 0);
    inputLayout->setSpacing(0);

    // 初始化上下文控件
    {
        mContextItemContainer = new ContextItemContainer(this);
        mContextItemContainer->setVisible(false);

        connect(mContextItemContainer, &ContextItemContainer::contextRemoved, this, [this](const ContextItem &context){
            mTextEdit->removeContextTag(context);
        });

        inputLayout->addWidget(mContextItemContainer);
    }

    // 初始化代码段控件容器
    {
        mSnippetContainerLayout = new QVBoxLayout();
        mSnippetContainerLayout->setContentsMargins(0, 0, 0, 0);
        mSnippetContainerLayout->setSpacing(0);
        inputLayout->addLayout(mSnippetContainerLayout, 0);

        connect(CodeBoosterPlugin::instance(), &CodeBoosterPlugin::documentSelectionChanged, this, &InputWidget::onShowCodeSnippet);
    }

    // 初始化文本编辑控件
    {
        QHBoxLayout *textEditLayout = new QHBoxLayout();
        textEditLayout->setSpacing(0);
        textEditLayout->setContentsMargins(0, 0, 0, 0);

        {
            mTextEdit = new CustomTextEdit(this);
            connect(mTextEdit, &CustomTextEdit::focusChange, this, &InputWidget::onTextEditFocusChange);
            connect(mTextEdit, &CustomTextEdit::sendMessage, this, [this]{
                if (!mInStreaming)
                {
                    onSendButtonClicked();

                }
            });
            connect(mTextEdit, &CustomTextEdit::newChat, this, [this]{
                QString message = mTextEdit->toPlainText().simplified();
                if (!mInStreaming)
                {
                    emit createNewChat();
                }
            });
            connect(mTextEdit, &CustomTextEdit::focusModeShortcutPress, this, [this](){
                mFocusEditorAction->trigger();
            });
            connect(mTextEdit, &CustomTextEdit::newContextItem, this, [this](const ContextItem &item){
                // 第一次添加上下文时显示控件
                mContextItemContainer->setVisible(true);
                mContextItemContainer->newContextItemWgt(item);
            });

            textEditLayout->addWidget(mTextEdit);
        }

        inputLayout->addLayout(textEditLayout, 1);
    }

    // 初始化样式
    onTextEditFocusChange(false);
}

InputWidget::~InputWidget()
{

}

void InputWidget::waitingForReceiveMsg()
{
    mInStreaming = true;
}

void InputWidget::messageReceiveFinished()
{
    mInStreaming = false;
}

void InputWidget::setShowEditorSelection(bool show)
{
    mShowSnippet = show;
    if (show == false)
    {
        // 清除所有非置顶的代码片段
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (!widget->isPinned()) {
                mSnippetContainerLayout->removeWidget(widget);
                widget->deleteLater();
                it = mCodeSnippetWidgets.erase(it);
            } else {
                ++it;
            }
        }
    }
}

void InputWidget::activateInput()
{
    mTextEdit->setFocus();

    // 自动显示文本编辑器选中的代码
    // 检查是否有非置顶的代码片段显示当前选中内容
    bool hasActiveSnippet = false;
    for (CodeSnippetWidget* widget : mCodeSnippetWidgets) {
        if (!widget->isPinned() && !widget->codeSnippet().isEmpty()) {
            hasActiveSnippet = true;
            break;
        }
    }

    if (!hasActiveSnippet) {
        auto textEditor = BaseTextEditor::currentTextEditor();
        if (!textEditor)
            return;
        TextEditorWidget *widget = textEditor->editorWidget();

        QString fileName = widget->textDocument()->filePath().fileName();
        QString snippet = widget->selectedText();

        if(!snippet.isEmpty() && USER_OP.showEditorSelection) {
            // 计算当前选中文本的行号
            QTextCursor cursor = widget->textCursor();
            int startLine = 1, endLine = 1;
            if (cursor.hasSelection()) {
                int selectionStart = cursor.selectionStart();
                int selectionEnd = cursor.selectionEnd();

                QTextCursor startCursor(widget->document());
                startCursor.setPosition(selectionStart);
                startLine = startCursor.blockNumber() + 1;

                QTextCursor endCursor(widget->document());
                endCursor.setPosition(selectionEnd);
                endLine = endCursor.blockNumber() + 1;
            }
            onShowCodeSnippet(fileName, snippet, startLine, endLine);
        }
    }
}

void InputWidget::setText(const QString &text)
{
    mTextEdit->setPlainText(text);
}

void InputWidget::onSendButtonClicked()
{
    if (!mInStreaming)
    {
        QString message = mTextEdit->toPlainText().trimmed();

        // 收集所有可见的代码片段
        QStringList codeSnippets;
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (widget->isVisible() && !widget->codeSnippet().isEmpty()) {
                codeSnippets.append(widget->codeSnippet());

                // 如果不是置顶的，发送后清除
                if (!widget->isPinned()) {
                    mSnippetContainerLayout->removeWidget(widget);
                    widget->deleteLater();
                    it = mCodeSnippetWidgets.erase(it);
                    continue;
                }
            }
            ++it;
        }

        // 将代码片段添加到消息前面
        if (!codeSnippets.isEmpty()) {
            message.prepend(QString("%1\n\n").arg(codeSnippets.join("\n\n")));
        }

        if (message.isEmpty())
            return;

        if (mFocusEditorAction->isChecked())
        {
            // 发送消息后退出专注编辑模式
            mFocusEditorAction->trigger();
        }

        setText(QString());

        waitingForReceiveMsg();

        QList<ContextItem> contexts = mContextItemContainer->allContext();
        mContextItemContainer->clearContext();

        emit sendUserMessage(message, contexts);
    }
}

void InputWidget::currentRequestStoped()
{

}

QList<ContextItem> InputWidget::contexts() const
{
    return mContextItemContainer->allContext();
}

bool InputWidget::event(QEvent *event)
{
    if (event->type() == QEvent::Show)
    {
        QTimer::singleShot(10, [this]() {
            activateInput();
        });
    }

    return QFrame::event(event);
}


void InputWidget::onShowCodeSnippet(const QString &fileName, const QString &text, int startLine, int endLine)
{
    if (!USER_OP.showEditorSelection)
        return;

    if (!mShowSnippet)
        return;

    // 如果文本为空，清除所有非置顶的代码片段
    if (text.trimmed().isEmpty()) {
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (!widget->isPinned()) {
                mSnippetContainerLayout->removeWidget(widget);
                widget->deleteLater();
                it = mCodeSnippetWidgets.erase(it);
            } else {
                ++it;
            }
        }
        return;
    }

    // 查找是否已有置顶的代码片段
    bool hasPinnedWidget = false;
    for (CodeSnippetWidget* widget : mCodeSnippetWidgets) {
        if (widget->isPinned()) {
            hasPinnedWidget = true;
            break;
        }
    }

    // 如果有置顶的代码片段，需要创建新的代码片段控件
    if (hasPinnedWidget) {
        // 先清除所有非置顶的代码片段
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (!widget->isPinned()) {
                mSnippetContainerLayout->removeWidget(widget);
                widget->deleteLater();
                it = mCodeSnippetWidgets.erase(it);
            } else {
                ++it;
            }
        }

        // 创建新的代码片段控件
        createNewCodeSnippetWidget(fileName, text, startLine, endLine);
    } else {
        // 没有置顶的代码片段，使用或创建一个普通的代码片段控件
        CodeSnippetWidget* activeWidget = nullptr;

        // 查找第一个非置顶的代码片段控件
        for (CodeSnippetWidget* widget : mCodeSnippetWidgets) {
            if (!widget->isPinned()) {
                activeWidget = widget;
                break;
            }
        }

        // 如果没有找到，创建一个新的
        if (!activeWidget) {
            activeWidget = createNewCodeSnippetWidget(fileName, text, startLine, endLine);
        } else {
            activeWidget->showCodeSnippet(fileName, text, startLine, endLine);
        }
    }
}

void InputWidget::onTextEditFocusChange(bool focus)
{
    QString styleSheetStr;

    if (focus)
    {
        styleSheetStr = QString("QFrame#InputWidget { border: 1px solid #005BBE; background-color: %1; }").arg(CB_THEME.Color_NomalBackground);
    }
    else
    {
        styleSheetStr = QString("QFrame#InputWidget { border: 1px solid #A8A8A9; background-color: %1; }").arg(CB_THEME.Color_NomalBackground);
    }

    // mMainInputContainer->setStyleSheet(styleSheetStr);
    setStyleSheet(styleSheetStr);
}

void InputWidget::onUseCurrentFileClicked(bool checked)
{
    using namespace Core;

    if (const IDocument *document = EditorManager::currentDocument())
    {
        bool ok = true;
        ContextItem item = ContextItem::buildFileContextFromFilePath(document->filePath(), ok);

        if (ok)
        {
            mTextEdit->addContextItem(item);
        }
    }
}

CodeSnippetWidget* InputWidget::createNewCodeSnippetWidget(const QString &fileName, const QString &text, int startLine, int endLine)
{
    CodeSnippetWidget* newWidget = new CodeSnippetWidget(this);

    // 连接信号槽
    connect(newWidget, &CodeSnippetWidget::closeRequested, this, [this, newWidget]() {
        // 从布局和列表中移除控件
        mSnippetContainerLayout->removeWidget(newWidget);
        mCodeSnippetWidgets.removeOne(newWidget);
        newWidget->deleteLater();
    });

    connect(newWidget, &CodeSnippetWidget::pinStateChanged, this, [this](bool pinned) {
        // 置顶状态变化时的处理逻辑已在onShowCodeSnippet中处理
        Q_UNUSED(pinned)
    });

    // 添加到布局和列表
    mSnippetContainerLayout->addWidget(newWidget);
    mCodeSnippetWidgets.append(newWidget);

    // 显示代码片段
    newWidget->showCodeSnippet(fileName, text, startLine, endLine);

    return newWidget;
}

}
