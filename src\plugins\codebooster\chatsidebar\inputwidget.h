#ifndef INPUTWIDGET_H
#define INPUTWIDGET_H

#include <QPlainTextEdit>
#include <QResizeEvent>
#include <QPushButton>
#include <QLabel>
#include <QToolBar>
#include <QToolButton>
#include <QCheckBox>

#include <solutions/spinner/spinner.h>

#include "contextitem.h"

class NotePreviewWidget;

namespace CodeBooster::Internal{

class CustomLineWidget;

/**
 * @brief The CodeSnippetWidget class 代码段控件
 */
class CodeSnippetWidget : public QFrame
{
    Q_OBJECT
public:
    explicit CodeSnippetWidget(QWidget *parent = nullptr);

public:
    void showCodeSnippet(const QString &fileName, const QString &selectedText, int startLine = 1, int endLine = 1);
    QString codeSnippet() const;
    void clear();

    // 置顶相关功能
    bool isPinned() const { return mIsPinned; }
    void setPinned(bool pinned);

protected:
    void resizeEvent(QResizeEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event);

signals:
    void heightChanged(int height);
    void pinStateChanged(bool pinned); // 置顶状态变化信号
    void closeRequested(); // 关闭请求信号

private slots:
    void onActionCloseTriggered();
    void onActionExpandTriggered();
    void onActionPinTriggered(); // 置顶按钮槽函数

private:
    void updateFileNameDisplay(); // 更新文件名显示（处理省略）

private:
    bool mCodeMode;
    bool mIsPinned; // 置顶状态

    QVBoxLayout *mLayout;

    QToolBar *mToolBar;
    QLabel *mFileIcon;
    QLabel *mFileNameTitle;
    QAction *mActionClose;
    QAction *mActionExpand;
    QAction *mActionPin; // 置顶按钮

    CustomLineWidget *mHorLine;

    NotePreviewWidget *mPreviewWgt;
    QString mFileName;
    QString mCodeSnippet;
    int mStartLine; // 开始行号
    int mEndLine;   // 结束行号
};

// -------------------------------------------------------------------------------
// -------------------------------------------------------------------------------

class LineNumberArea;

/**
 * @brief The CustomTextEdit class 自定义的文本输入框
 */
class CustomTextEdit : public QPlainTextEdit
{
    Q_OBJECT

public:
    CustomTextEdit(QWidget *parent = nullptr);

    void setFocusMode(bool enable);
    void lineNumberAreaPaintEvent(QPaintEvent *event);
    int lineNumberAreaWidth();

    void addContextItem(const ContextItem &item);
    void removeContextTag(const ContextItem &context);


protected:
    bool event(QEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void focusInEvent(QFocusEvent *event) override;
    void focusOutEvent(QFocusEvent *event) override;
    void keyPressEvent(QKeyEvent* event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private:
    void setPlaceholderTextVisible(bool visible);
    void processDirectory(const QString &path, QString &content);

private slots:
    void adjustInputEditSize();
    void updateLineNumberAreaWidth(int newBlockCount = 0);
    void highlightCurrentLine(bool highlight);
    void updateLineNumberArea(const QRect &, int dy);

signals:
    void sizeChanged();
    void heightChanged(int height);
    void sendMessage();
    void newChat();
    void focusChange(bool focus);
    void focusModeShortcutPress();
    void newContextItem(const ContextItem &item);

private:
    bool mFocusMode;///< 专注编辑模式
    QWidget *lineNumberArea;

    // 常量
    int mMinInputHeight;
    int mMaxInputHeight;
    //

    struct ColorScheme
    {
        QColor lineNumberAreaBackground;
        QColor lineNumberText;
        QColor highlightLineBackground;
        QColor topSeparator;
    };

    ColorScheme mColorScheme;
};

class LineNumberArea : public QWidget
{
public:
    LineNumberArea(CustomTextEdit *editor) : QWidget(editor) {
        codeEditor = editor;
    }

    QSize sizeHint() const Q_DECL_OVERRIDE {
        return QSize(codeEditor->lineNumberAreaWidth(), 0);
    }

protected:
    void paintEvent(QPaintEvent *event) Q_DECL_OVERRIDE {
        codeEditor->lineNumberAreaPaintEvent(event);
    }

private:
    CustomTextEdit *codeEditor;
};

// -------------------------------------------------------------------------------
// -------------------------------------------------------------------------------

class ContextItemContainer;

/**
 * @brief The InputWidget class 输入控件区域
 */
class InputWidget : public QFrame
{
    Q_OBJECT

public:
    InputWidget(QWidget *parent = nullptr);
    ~InputWidget();

    static bool defaultShowEditorSelection() {return true;}

public:
    void waitingForReceiveMsg();
    void messageReceiveFinished();
    void setShowEditorSelection(bool show);

    void activateInput();

    void setText(const QString &text);
    void onSendButtonClicked();
    void currentRequestStoped();

    QList<ContextItem> contexts() const;

protected:
    bool event(QEvent *event) override;

signals:
    void sendUserMessage(const QString &message, const QList<ContextItem> &contexts);
    void createNewChat();
    void inputFocusModeChanged(bool enable);

private slots:
    void onShowCodeSnippet(const QString &fileName, const QString &text, int startLine, int endLine);
    void onTextEditFocusChange(bool focus);
    void onUseCurrentFileClicked(bool checked);

private:
    CodeSnippetWidget* createNewCodeSnippetWidget(const QString &fileName, const QString &text, int startLine = 1, int endLine = 1);

private:
    QFrame *mMainInputContainer;
    QString mBgColorStr;

    ContextItemContainer *mContextItemContainer;

    // 代码片段容器管理
    QVBoxLayout *mSnippetContainerLayout;
    QList<CodeSnippetWidget*> mCodeSnippetWidgets; // 管理多个代码片段控件
    bool mShowSnippet;

    CustomTextEdit *mTextEdit;
    bool mInStreaming;

    QToolBar *mToolBar;
    QAction *mUseCurrentFileAction;
    QAction *mFocusEditorAction;
    QCheckBox *mUserCurrentFileChk;
    QAction *mUserCurrentFileAction;

};

}

#endif // INPUTWIDGET_H
